[gd_scene load_steps=4 format=3 uid="uid://bv5ktpmwow0o7"]

[ext_resource type="Script" uid="uid://0lax4sx4ont5" path="res://scenes/objects/break_glass.gd" id="1_1rj8y"]
[ext_resource type="Texture2D" uid="uid://dkf0m40l3wrd4" path="res://images/objects/glasses/1185A0.png" id="1_yd17t"]

[sub_resource type="BoxShape3D" id="BoxShape3D_tfoj6"]
size = Vector3(0.0605469, 0.187256, 0.0622559)

[node name="Glass10" type="StaticBody3D"]
collision_layer = 4
collision_mask = 7
script = ExtResource("1_1rj8y")

[node name="Sprite3D" type="Sprite3D" parent="."]
billboard = 2
shaded = true
texture = ExtResource("1_yd17t")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_tfoj6")
