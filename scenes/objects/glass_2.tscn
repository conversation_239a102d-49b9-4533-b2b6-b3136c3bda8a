[gd_scene load_steps=4 format=3 uid="uid://cb47gif1io5ab"]

[ext_resource type="Texture2D" uid="uid://dht5kpbhj6dam" path="res://images/objects/glasses/1175A0.png" id="1_831bt"]
[ext_resource type="Script" uid="uid://0lax4sx4ont5" path="res://scenes/objects/break_glass.gd" id="1_lsvjl"]

[sub_resource type="BoxShape3D" id="BoxShape3D_t5nx1"]
size = Vector3(0.0537109, 0.102539, 0.0751953)

[node name="Glass2" type="StaticBody3D"]
collision_layer = 4
collision_mask = 7
script = ExtResource("1_lsvjl")

[node name="Sprite3D" type="Sprite3D" parent="."]
billboard = 2
shaded = true
texture = ExtResource("1_831bt")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_t5nx1")
