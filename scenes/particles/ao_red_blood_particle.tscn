[gd_scene load_steps=7 format=3 uid="uid://c40i7sucfv2y"]

[ext_resource type="Texture2D" uid="uid://cgadwfimf3l3i" path="res://images/particles/BLUDC0.png" id="1_pmgxl"]
[ext_resource type="Texture2D" uid="uid://db84hqxh4clro" path="res://images/particles/BLUDB0.png" id="2_qtynr"]
[ext_resource type="Texture2D" uid="uid://dee8d0p1ntatd" path="res://images/particles/BLUDA0.png" id="3_f725a"]

[sub_resource type="GDScript" id="GDScript_pmgxl"]
script/source = "extends RigidBody3D

@onready var animated_sprite_3d: AnimatedSprite3D = $AnimatedSprite3D


func _on_animated_sprite_3d_animation_finished() -> void:
	queue_free()


func _physics_process(delta: float) -> void:
	if(linear_velocity.length() < 0.01):
		animated_sprite_3d.position.y -= 0.02 * delta
"

[sub_resource type="SpriteFrames" id="SpriteFrames_j4ola"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": ExtResource("1_pmgxl")
}, {
"duration": 1.0,
"texture": ExtResource("2_qtynr")
}, {
"duration": 1.0,
"texture": ExtResource("3_f725a")
}],
"loop": false,
"name": &"default",
"speed": 2.0
}]

[sub_resource type="SphereShape3D" id="SphereShape3D_pmgxl"]
radius = 0.0377624

[node name="AoRedBloodParticle" type="RigidBody3D"]
collision_layer = 0
collision_mask = 4
mass = 0.2
script = SubResource("GDScript_pmgxl")

[node name="AnimatedSprite3D" type="AnimatedSprite3D" parent="."]
billboard = 2
shaded = true
render_priority = 1
sprite_frames = SubResource("SpriteFrames_j4ola")
autoplay = "default"

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.0304538, 0)
shape = SubResource("SphereShape3D_pmgxl")

[connection signal="animation_finished" from="AnimatedSprite3D" to="." method="_on_animated_sprite_3d_animation_finished"]
