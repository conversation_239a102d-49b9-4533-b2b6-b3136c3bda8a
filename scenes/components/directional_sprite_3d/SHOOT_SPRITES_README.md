# DirectionalSprite3D - Shoot Sprites Feature

The DirectionalSprite3D component now supports **Shoot Sprites** - a new sprite type that displays when entities are shooting or have just shot something.

## Overview

Shoot sprites work alongside the existing idle and movement sprites, with the following priority system:

1. **Shooting State** (Highest Priority) - Shows when `shooting_state` is "shooting" or "shot"
2. **Movement State** (Medium Priority) - Shows when `moving_state` is "run" or "moving"  
3. **Idle State** (Lowest Priority) - Default fallback when stationary and not shooting

## Setup

### 1. Add shooting_state Property

Add a `shooting_state` property to your entity script:

```gdscript
extends CharacterBody3D

var moving_state: String = "idle"     # For movement sprites
var shooting_state: String = ""       # For shoot sprites - NEW!
```

### 2. Configure Shoot Sprites in Inspector

Once your entity has a `shooting_state` property, the DirectionalSprite3D component will automatically show **"Shoot sprites"** in the inspector.

For THREE_DIRECTIONAL mode, you'll see:
- `front_shoot_sprites` - Array of textures for front-facing shooting
- `side_shoot_sprites` - Array of textures for side-facing shooting  
- `back_shoot_sprites` - Array of textures for back-facing shooting

### 3. Manage Shooting State

Control when shoot sprites are displayed by setting the `shooting_state`:

```gdscript
# Start shooting (shows shoot sprites)
func start_shooting():
    shooting_state = "shooting"
    # Your shooting logic here...

# Stop shooting (returns to movement/idle sprites)
func stop_shooting():
    shooting_state = ""

# Single shot (brief shoot sprite display)
func fire_single_shot():
    shooting_state = "shot"
    await get_tree().create_timer(0.2).timeout
    shooting_state = ""
```

## Integration Examples

### With Weapon Manager

```gdscript
func _on_weapon_fired():
    shooting_state = "shooting"

func _on_weapon_stopped_firing():
    shooting_state = ""
```

### With Input System

```gdscript
func _input(event):
    if event.is_action_pressed("shoot"):
        shooting_state = "shooting"
    elif event.is_action_released("shoot"):
        shooting_state = ""
```

## Technical Details

### Atlas Generation
- Shoot sprites are included in the texture atlas alongside idle and movement sprites
- Atlas layout per direction: `[idle][movement_frames...][shoot_frames...]`
- Maintains compatibility with existing atlas generation and bleeding prevention

### Shader Integration
- New `sprite_state` uniform (0=idle, 1=movement, 2=shooting)
- Automatic frame offset calculation based on sprite type and frame counts
- Per-camera independence preserved

### State Detection
The component automatically detects the `shooting_state` property using reflection:
- Checks for `shooting_state` property in target node's script
- Shows "Shoot sprites" in inspector only when property exists
- No performance impact when shooting_state is not used

## Compatibility

- ✅ Works with all direction modes (THREE, FOUR, FIVE, EIGHT)
- ✅ Compatible with existing idle and movement sprites
- ✅ Maintains per-camera independence
- ✅ Supports texture atlas generation
- ✅ Works with billboard-Y rendering mode
- ✅ Preserves DirectionalSprite3D texture properties (compress/high_quality)

## Example Entity

See `example_shooting_entity.gd` for a complete implementation example showing:
- Property setup
- Shooting state management
- Integration with movement system
- Single-shot vs continuous shooting patterns

## Migration

Existing DirectionalSprite3D setups are fully compatible:
- No changes needed to existing idle/movement sprite configurations
- Shoot sprites are optional - component works normally without them
- Performance impact is minimal when shoot sprites are not used
