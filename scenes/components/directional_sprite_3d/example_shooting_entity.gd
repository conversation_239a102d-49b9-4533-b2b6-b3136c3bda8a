extends CharacterBody3D
class_name ExampleShootingEntity

## Example entity that demonstrates how to use DirectionalSprite3D with shoot sprites
##
## This script shows how to implement the shooting_state property that DirectionalSprite3D
## looks for to determine when to display shoot sprites.

# Movement and animation states
var moving_state: String = "idle"  # "idle" or "run" - used by DirectionalSprite3D for movement sprites
var shooting_state: String = ""    # "" (empty), "shooting", or "shot" - used by DirectionalSprite3D for shoot sprites

# Shooting mechanics
var is_shooting: bool = false
var shoot_duration: float = 0.5  # How long to show shoot sprites
var shoot_timer: float = 0.0

# Movement
const SPEED = 5.0

@onready var directional_sprite: DirectionalSprite3D = $Graphics/DirectionalSprite3D


func _ready() -> void:
	# The DirectionalSprite3D component will automatically detect the shooting_state
	# and moving_state properties and show the appropriate sprite group in the inspector
	pass


func _physics_process(delta: float) -> void:
	# Handle movement
	_handle_movement(delta)
	
	# Handle shooting
	_handle_shooting(delta)
	
	# Update animation states
	_update_animation_states()
	
	move_and_slide()


func _handle_movement(delta: float) -> void:
	# Simple movement example
	var input_dir = Vector2.ZERO
	
	if Input.is_action_pressed("ui_right"):
		input_dir.x += 1
	if Input.is_action_pressed("ui_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("ui_down"):
		input_dir.y += 1
	if Input.is_action_pressed("ui_up"):
		input_dir.y -= 1
	
	if input_dir != Vector2.ZERO:
		velocity.x = input_dir.x * SPEED
		velocity.z = input_dir.y * SPEED
	else:
		velocity.x = move_toward(velocity.x, 0, SPEED)
		velocity.z = move_toward(velocity.z, 0, SPEED)


func _handle_shooting(delta: float) -> void:
	# Handle shooting input
	if Input.is_action_just_pressed("ui_accept") and not is_shooting:
		start_shooting()
	
	# Update shooting timer
	if is_shooting:
		shoot_timer -= delta
		if shoot_timer <= 0:
			stop_shooting()


func _update_animation_states() -> void:
	# Update movement state
	if velocity.length() > 0.1:
		moving_state = "run"
	else:
		moving_state = "idle"
	
	# Shooting state is managed by start_shooting() and stop_shooting()


func start_shooting() -> void:
	"""Start shooting - this will trigger the DirectionalSprite3D to show shoot sprites"""
	is_shooting = true
	shooting_state = "shooting"
	shoot_timer = shoot_duration
	
	# Add your shooting logic here (spawn projectiles, play sounds, etc.)
	print("Started shooting!")


func stop_shooting() -> void:
	"""Stop shooting - this will make DirectionalSprite3D return to movement/idle sprites"""
	is_shooting = false
	shooting_state = ""
	
	print("Stopped shooting!")


## Alternative shooting state management
## You can also use "shot" state for single-shot weapons or brief shooting animations

func fire_single_shot() -> void:
	"""Example of single shot that briefly shows shoot sprite then returns to normal"""
	shooting_state = "shot"
	
	# Add your single shot logic here
	print("Fired single shot!")
	
	# Return to normal state after a brief delay
	await get_tree().create_timer(0.2).timeout
	shooting_state = ""


## Integration with weapon systems
## If you have a weapon manager, you can integrate it like this:

func _on_weapon_fired() -> void:
	"""Called when weapon manager fires a weapon"""
	start_shooting()


func _on_weapon_stopped_firing() -> void:
	"""Called when weapon manager stops firing"""
	stop_shooting()


## Notes for integration:
## 
## 1. The DirectionalSprite3D component automatically detects the shooting_state property
##    and will show "Shoot sprites" in the inspector when this script is attached.
##
## 2. Shoot sprites take priority over movement and idle sprites when shooting_state 
##    is set to "shooting" or "shot".
##
## 3. When shooting_state is empty (""), the component falls back to movement sprites
##    (if moving) or idle sprites (if stationary).
##
## 4. You can set up shoot sprites in the inspector just like idle and movement sprites:
##    - For THREE_DIRECTIONAL mode: front_shoot_sprites, side_shoot_sprites, back_shoot_sprites
##    - Each direction can have multiple frames for animated shooting
##
## 5. The shooting state integrates seamlessly with the existing atlas generation and
##    per-camera independence features.
