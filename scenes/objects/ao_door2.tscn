[gd_scene load_steps=8 format=3 uid="uid://coi5t3tlf2d2p"]

[ext_resource type="Script" uid="uid://d2g5jvjq61kgj" path="res://scenes/objects/Door.gd" id="1_hqq76"]
[ext_resource type="ArrayMesh" uid="uid://dlc0vb52u0mlc" path="res://models/mansion1/doors/door2.res" id="1_wwxnc"]
[ext_resource type="AudioStream" uid="uid://cwtqpiklh7vhx" path="res://sounds/sfx/DOORLOCK.ogg" id="2_8ie41"]

[sub_resource type="BoxShape3D" id="BoxShape3D_wwxnc"]
size = Vector3(1.28, 2.56, 0.16)

[sub_resource type="Animation" id="Animation_c8fhn"]
resource_name = "Open"
length = 0.8
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimatableBody3D:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.8),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 1.39626, 0)]
}

[sub_resource type="Animation" id="Animation_wwxnc"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimatableBody3D:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_wwxnc"]
_data = {
&"Open": SubResource("Animation_c8fhn"),
&"RESET": SubResource("Animation_wwxnc")
}

[node name="AoDoor2" type="Node3D" groups=["door"]]
script = ExtResource("1_hqq76")
time_to_close = 3.3
locked_sound = ExtResource("2_8ie41")
can_interrupt = false
allow_back = false
allow_left = true
allow_right = true
allow_top = true
allow_bottom = true

[node name="AnimatableBody3D" type="AnimatableBody3D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="MeshInstance3D" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.64, 0, 0)
mesh = ExtResource("1_wwxnc")

[node name="CollisionShape3D" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.64, 0, 0)
shape = SubResource("BoxShape3D_wwxnc")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_wwxnc")
}
