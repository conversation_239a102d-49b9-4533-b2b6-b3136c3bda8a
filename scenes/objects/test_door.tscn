[gd_scene load_steps=8 format=3 uid="uid://btyxvfk53peer"]

[ext_resource type="Script" uid="uid://5rpvqlwl2cly" path="res://scenes/objects/door_test.gd" id="1_fg03c"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_010u4"]
transparency = 1
cull_mode = 2
depth_draw_mode = 1

[sub_resource type="BoxMesh" id="BoxMesh_010u4"]
material = SubResource("StandardMaterial3D_010u4")
size = Vector3(1.28, 2.56, 0.307)

[sub_resource type="BoxShape3D" id="BoxShape3D_a4yma"]
size = Vector3(1.28, 2.56, 0.307)

[sub_resource type="Animation" id="Animation_a4yma"]
resource_name = "Open"
length = 2.0
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("..:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 2.5, 0)]
}

[sub_resource type="Animation" id="Animation_8paui"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("..:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8paui"]
_data = {
&"Open": SubResource("Animation_a4yma"),
&"RESET": SubResource("Animation_8paui")
}

[node name="TestDoor" type="Node3D" groups=["door"]]
script = ExtResource("1_fg03c")

[node name="AnimatableBody3D" type="AnimatableBody3D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="MeshInstance3D" type="MeshInstance3D" parent="AnimatableBody3D"]
mesh = SubResource("BoxMesh_010u4")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="AnimatableBody3D"]
shape = SubResource("BoxShape3D_a4yma")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("../AnimatableBody3D/MeshInstance3D")
libraries = {
&"": SubResource("AnimationLibrary_8paui")
}
