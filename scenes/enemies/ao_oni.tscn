[gd_scene load_steps=23 format=3 uid="uid://b1vimowqmce5w"]

[ext_resource type="Script" uid="uid://drjk48niw8xym" path="res://scenes/enemies/ao_oni.gd" id="1_xmjcu"]
[ext_resource type="Texture2D" uid="uid://bhgrkq04i5nfs" path="res://images/enemies/ao-oni/AONIA4A6.png" id="2_bgw0k"]
[ext_resource type="Texture2D" uid="uid://b1pskcfv7wyvc" path="res://images/enemies/ao-oni/AONIA1.png" id="2_iaafa"]
[ext_resource type="Texture2D" uid="uid://qe7ijko24ssb" path="res://images/enemies/ao-oni/AONIB4B6.png" id="2_u6v1l"]
[ext_resource type="Texture2D" uid="uid://bhos8kwin57l5" path="res://images/enemies/ao-oni/AONIB1.png" id="2_xkk46"]
[ext_resource type="Texture2D" uid="uid://c4dgs5h82d03n" path="res://images/enemies/ao-oni/AONIA3A7.png" id="3_bu3lp"]
[ext_resource type="Texture2D" uid="uid://bfv3dygs83x66" path="res://images/enemies/ao-oni/AONIC5.png" id="4_c3h1a"]
[ext_resource type="Texture2D" uid="uid://beijksyrqiunb" path="res://images/enemies/ao-oni/AONIC1.png" id="4_y64dm"]
[ext_resource type="Texture2D" uid="uid://b3q8cfyh37g3u" path="res://images/enemies/ao-oni/AONIC3C7.png" id="5_k2h66"]
[ext_resource type="Texture2D" uid="uid://vgfc4lpa8uoh" path="res://images/enemies/ao-oni/AONIB3B7.png" id="7_aoo63"]
[ext_resource type="Script" uid="uid://b5x4ehxe1tlru" path="res://dynamic_footstep_system/scripts/footstep_surface_detector.gd" id="11_di6fq"]
[ext_resource type="Resource" uid="uid://dvab7my8m4tta" path="res://dynamic_footstep_system/dark_chasers_material_library.tres" id="12_hau6t"]

[sub_resource type="Animation" id="Animation_2yldm"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("2_iaafa")]
}

[sub_resource type="Animation" id="Animation_utq5j"]
resource_name = "run-back"
length = 0.4
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [ExtResource("2_u6v1l"), ExtResource("2_bgw0k"), ExtResource("4_c3h1a"), ExtResource("2_bgw0k")]
}
tracks/1/type = "method"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../FootstepSurfaceDetector")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"values": [{
"args": [],
"method": &"play_footstep"
}, {
"args": [],
"method": &"play_footstep"
}]
}

[sub_resource type="Animation" id="Animation_r4ike"]
resource_name = "run-front"
length = 0.4
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [ExtResource("2_xkk46"), ExtResource("2_iaafa"), ExtResource("4_y64dm"), ExtResource("2_iaafa")]
}
tracks/1/type = "method"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../FootstepSurfaceDetector")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"values": [{
"args": [],
"method": &"play_footstep"
}, {
"args": [],
"method": &"play_footstep"
}]
}

[sub_resource type="Animation" id="Animation_sqno1"]
resource_name = "run-side"
length = 0.4
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [ExtResource("7_aoo63"), ExtResource("3_bu3lp"), ExtResource("5_k2h66"), ExtResource("3_bu3lp")]
}
tracks/1/type = "method"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../FootstepSurfaceDetector")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"values": [{
"args": [],
"method": &"play_footstep"
}, {
"args": [],
"method": &"play_footstep"
}]
}

[sub_resource type="Animation" id="Animation_ct5r0"]
resource_name = "stay-back"
length = 0.1
loop_mode = 2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("2_bgw0k")]
}

[sub_resource type="Animation" id="Animation_7ib5g"]
resource_name = "stay-front"
length = 0.1
loop_mode = 2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("2_iaafa")]
}

[sub_resource type="Animation" id="Animation_myl4e"]
resource_name = "stay-side"
length = 0.1
loop_mode = 2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite3D:texture")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("3_bu3lp")]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ali5n"]
_data = {
&"RESET": SubResource("Animation_2yldm"),
&"run-back": SubResource("Animation_utq5j"),
&"run-front": SubResource("Animation_r4ike"),
&"run-side": SubResource("Animation_sqno1"),
&"stay-back": SubResource("Animation_ct5r0"),
&"stay-front": SubResource("Animation_7ib5g"),
&"stay-side": SubResource("Animation_myl4e")
}

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_dl8o4"]
radius = 0.29
height = 1.0716

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_qvs6v"]
radius = 0.57991
height = 2.05769

[node name="Ao oni" type="CharacterBody3D" groups=["enemy"]]
collision_layer = 2
collision_mask = 45
slide_on_ceiling = false
wall_min_slide_angle = 0.0
floor_stop_on_slope = false
floor_block_on_wall = false
floor_snap_length = 0.31
script = ExtResource("1_xmjcu")

[node name="Interaction" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.161787, 0)
target_position = Vector3(0, 0, -1.1)
collision_mask = 12

[node name="Graphics" type="Node3D" parent="."]

[node name="Sprite3D" type="Sprite3D" parent="Graphics"]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
offset = Vector2(0, 194)
billboard = 2
shaded = true
render_priority = 1
texture = ExtResource("2_iaafa")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Graphics"]
libraries = {
&"": SubResource("AnimationLibrary_ali5n")
}

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.55, 0)
shape = SubResource("CapsuleShape3D_dl8o4")

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]
avoidance_enabled = true
radius = 1.0

[node name="KillZone" type="Area3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.05674, 0)
collision_layer = 0

[node name="CollisionShape3D" type="CollisionShape3D" parent="KillZone"]
shape = SubResource("CapsuleShape3D_qvs6v")

[node name="Timers" type="Node" parent="."]

[node name="InteractionTimer" type="Timer" parent="Timers"]
wait_time = 0.55
autostart = true

[node name="FindPathTimer" type="Timer" parent="Timers"]
wait_time = 0.1
autostart = true

[node name="WanderingTimer" type="Timer" parent="Timers"]
one_shot = true

[node name="FootstepSurfaceDetector" type="AudioStreamPlayer3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.18727, 0)
attenuation_model = 2
volume_db = -20.0
script = ExtResource("11_di6fq")
footstep_material_library = ExtResource("12_hau6t")

[connection signal="link_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_link_reached"]
[connection signal="target_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_target_reached"]
[connection signal="waypoint_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_waypoint_reached"]
[connection signal="body_entered" from="KillZone" to="." method="_on_kill_zone_body_entered"]
[connection signal="timeout" from="Timers/InteractionTimer" to="." method="_on_interaction_timer_timeout"]
[connection signal="timeout" from="Timers/FindPathTimer" to="." method="_on_find_path_timer_timeout"]
[connection signal="timeout" from="Timers/WanderingTimer" to="." method="_on_wandering_timer_timeout"]
