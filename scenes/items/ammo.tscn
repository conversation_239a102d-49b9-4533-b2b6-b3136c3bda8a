[gd_scene load_steps=4 format=3 uid="uid://b8xk7n2m4vqpd"]

[ext_resource type="Script" path="res://scenes/items/ammo.gd" id="1_ammo"]
[ext_resource type="AudioStream" uid="uid://dv86w10agao07" path="res://sounds/sfx/DSWPNUP.wav" id="2_pickup_sound"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ammo"]
size = Vector3(0.5, 0.3, 0.5)

[node name="Ammo" type="Area3D" groups=["item"]]
collision_layer = 8
script = ExtResource("1_ammo")
ammo_value = 20
pickup_sound = ExtResource("2_pickup_sound")
event_string = "Picked up ammo."

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(0.25, 0, 0, 0, 0.15, 0, 0, 0, 0.25, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_ammo")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
