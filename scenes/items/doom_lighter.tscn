[gd_scene load_steps=5 format=3 uid="uid://bfhn6wshiv372"]

[ext_resource type="Texture2D" uid="uid://b4b0lxjduwj4b" path="res://images/items/LIT1H0.png" id="1_8kfoi"]
[ext_resource type="Script" uid="uid://ck4p816vkcs66" path="res://scenes/items/wieldable.gd" id="1_up2y7"]
[ext_resource type="Resource" uid="uid://b254pho30ji8e" path="res://weapon_manager/weapons/doom_lighter.tres" id="2_1ie13"]

[sub_resource type="BoxShape3D" id="BoxShape3D_8kfoi"]
size = Vector3(0.293564, 0.246067, 0.288145)

[node name="DoomLighter" type="Area3D" groups=["item"]]
collision_layer = 8
script = ExtResource("1_up2y7")
wieldable_item = ExtResource("2_1ie13")
event_string = "Picked up a Lighter"

[node name="Sprite3D" type="Sprite3D" parent="."]
transform = Transform3D(0.4, 0, 0, 0, 0.4, 0, 0, 0, 0.4, 0, 0, 0)
billboard = 2
shaded = true
texture = ExtResource("1_8kfoi")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00599769, 0.00824153, -0.00654888)
shape = SubResource("BoxShape3D_8kfoi")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
