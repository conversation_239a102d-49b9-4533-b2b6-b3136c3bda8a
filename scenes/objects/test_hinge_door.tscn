[gd_scene load_steps=7 format=3 uid="uid://db22nx6aflw7k"]

[ext_resource type="Script" uid="uid://5rpvqlwl2cly" path="res://scenes/objects/door_test.gd" id="1_rrum7"]

[sub_resource type="BoxMesh" id="BoxMesh_010u4"]
size = Vector3(1.28, 2.56, 0.307)

[sub_resource type="BoxShape3D" id="BoxShape3D_a4yma"]
size = Vector3(1.28, 2.56, 0.307)

[sub_resource type="Animation" id="Animation_a4yma"]
resource_name = "Open"
length = 0.5
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("..:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 1.39626, 0)]
}

[sub_resource type="Animation" id="Animation_8paui"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("..:rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8paui"]
_data = {
&"Open": SubResource("Animation_a4yma"),
&"RESET": SubResource("Animation_8paui")
}

[node name="TestDoor" type="Node3D" groups=["door"]]
script = ExtResource("1_rrum7")

[node name="AnimatableBody3D" type="AnimatableBody3D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="MeshInstance3D" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.64, 0, 0)
mesh = SubResource("BoxMesh_010u4")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.64, 0, 0)
shape = SubResource("BoxShape3D_a4yma")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("../AnimatableBody3D/MeshInstance3D")
libraries = {
&"": SubResource("AnimationLibrary_8paui")
}
