[gd_scene load_steps=17 format=4 uid="uid://5q2aomrq4gjw"]

[ext_resource type="Script" uid="uid://c765qag2pbwqq" path="res://scenes/rooms/room_1.gd" id="1_3lwtk"]
[ext_resource type="Texture2D" uid="uid://co5uy0k6hjm4t" path="res://images/skyboxes/space1.png" id="1_26bms"]
[ext_resource type="PackedScene" uid="uid://dr1cm2nba1v1x" path="res://scenes/objects/teleport.tscn" id="3_6e2tk"]
[ext_resource type="PackedScene" uid="uid://cojpeusa1t4t6" path="res://scenes/hud.tscn" id="4_vk5nm"]
[ext_resource type="PackedScene" uid="uid://dtnl3ap2wwpc5" path="res://scenes/enemies/giant_scorpion.tscn" id="5_p8nu4"]

[sub_resource type="PanoramaSkyMaterial" id="PanoramaSkyMaterial_r5ja7"]
panorama = ExtResource("1_26bms")

[sub_resource type="Sky" id="Sky_c7vkd"]
sky_material = SubResource("PanoramaSkyMaterial_r5ja7")

[sub_resource type="Environment" id="Environment_main8"]
background_mode = 2
sky = SubResource("Sky_c7vkd")
fog_enabled = true
fog_light_color = Color(0.576471, 0.694118, 0.913725, 1)
fog_density = 0.005
fog_sky_affect = 0.0

[sub_resource type="NavigationMesh" id="NavigationMesh_icseg"]
vertices = PackedVector3Array(43.256, 10.25, 8.3955, 43.506, 10.25, 8.1455, 43.506, 10.25, 1.6455, -42.494, 10.25, 1.6455, -42.494, 10.25, 8.1455, -42.244, 10.25, 8.3955, 43.506, 10.25, -89.1045, 43.506, 10.25, -95.3545, 32.756, 10.25, -95.3545, -31.744, 10.25, -95.3545, -42.494, 10.25, -95.3545, -42.494, 10.25, -89.1045, 32.756, 10.25, 8.1455, 43.256, 10.25, 8.3955, 43.506, 10.25, 1.6455, 43.506, 10.25, -4.8545, -42.494, 10.25, 1.6455, -42.244, 10.25, 8.3955, -31.744, 10.25, 8.1455, -42.494, 10.25, -4.8545, 43.506, 10.25, -82.6045, 43.506, 10.25, -89.1045, 32.756, 10.25, -95.3545, -31.744, 10.25, -95.3545, -42.494, 10.25, -89.1045, -42.494, 10.25, -82.6045, 43.506, 10.25, -50.1045, 22.006, 10.25, -95.3545, 11.256, 10.25, -95.3545, 43.506, 10.25, -43.6045, -42.494, 10.25, -37.3545, -42.494, 10.25, -30.8545, -20.994, 10.25, 8.1455, -10.244, 10.25, 8.1455, 43.506, 10.25, -76.1045, 43.506, 10.25, -82.6045, 32.756, 10.25, -95.3545, -31.744, 10.25, -95.3545, -42.494, 10.25, -82.6045, -42.494, 10.25, -76.1045, 32.756, 10.25, 8.1455, 43.506, 10.25, -4.8545, 43.506, 10.25, -11.3545, -42.494, 10.25, -11.3545, -42.494, 10.25, -4.8545, -31.744, 10.25, 8.1455, 43.506, 10.25, -69.6045, 43.506, 10.25, -76.1045, 32.756, 10.25, -95.3545, -31.744, 10.25, -95.3545, -42.494, 10.25, -76.1045, -42.494, 10.25, -69.6045, 32.756, 10.25, 8.1455, 43.506, 10.25, -11.3545, 43.506, 10.25, -17.8545, -42.494, 10.25, -17.8545, -42.494, 10.25, -11.3545, -31.744, 10.25, 8.1455, 43.506, 10.25, -69.6045, 32.756, 10.25, -95.3545, 22.006, 10.25, -95.3545, 43.506, 10.25, -63.1045, -20.994, 10.25, -95.3545, -31.744, 10.25, -95.3545, -42.494, 10.25, -69.6045, -42.494, 10.25, -63.1045, 22.006, 10.25, 8.1455, 32.756, 10.25, 8.1455, 43.506, 10.25, -17.8545, 43.506, 10.25, -24.3545, -42.494, 10.25, -17.8545, -31.744, 10.25, 8.1455, -20.994, 10.25, 8.1455, -42.494, 10.25, -24.3545, 11.256, 10.25, 8.1455, 22.006, 10.25, 8.1455, 43.506, 10.25, -37.3545, 43.506, 10.25, -43.6045, -42.494, 10.25, -43.6045, -42.494, 10.25, -37.3545, -10.244, 10.25, 8.1455, 0.505981, 10.25, 8.1455, 0.505981, 10.25, -95.3545, -42.494, 10.25, -43.6045, 0.505981, 10.25, 8.1455, 11.256, 10.25, 8.1455, 43.506, 10.25, -43.6045, 11.256, 10.25, -95.3545, -10.244, 10.25, -95.3545, -42.494, 10.25, -50.1045, -42.494, 10.25, -43.6045, 0.505981, 10.25, -95.3545, 43.506, 10.25, -56.6045, 43.506, 10.25, -63.1045, 22.006, 10.25, -95.3545, -20.994, 10.25, -95.3545, -42.494, 10.25, -63.1045, -42.494, 10.25, -56.6045, 22.006, 10.25, 8.1455, 43.506, 10.25, -24.3545, 43.506, 10.25, -30.8545, -42.494, 10.25, -30.8545, -42.494, 10.25, -24.3545, -20.994, 10.25, 8.1455, 43.506, 10.25, -50.1045, 43.506, 10.25, -56.6045, 22.006, 10.25, -95.3545, -20.994, 10.25, -95.3545, -42.494, 10.25, -56.6045, -42.494, 10.25, -50.1045, -10.244, 10.25, -95.3545, 22.006, 10.25, 8.1455, 43.506, 10.25, -30.8545, 43.506, 10.25, -37.3545, 5.25598, 1, 5.3955, 4.75598, 1, 5.6455, 4.75598, 1, 8.1455, 12.506, 1, 8.1455, 1.00598, 1, -5.3545, 1.00598, 1, -3.3545, 3.00598, 1, -3.3545, 3.00598, 1, -5.3545, -41.244, 1, -20.6045, -6.74402, 1, -11.3545, -4.74402, 1, -11.1045, -41.244, 1, -31.1045, -0.994019, 1, -5.6045, 1.00598, 1, -5.3545, 3.00598, 1, -5.3545, -4.74402, 1, -2.8545, -1.24402, 1, -2.8545, -0.994019, 1, -5.6045, -4.74402, 1, -11.1045, -0.994019, 1, -5.6045, 3.00598, 1, -5.3545, 5.00598, 1, -5.6045, 42.256, 1, -36.3545, 42.256, 1, -42.6045, -4.74402, 1, -11.1045, 35.756, 1, 8.1455, 36.006, 1, 9.1455, 42.256, 1, 9.1455, 42.256, 1, 2.6455, 42.256, 1, -49.1045, 42.256, 1, -55.6045, 21.256, 1, -94.1045, 10.756, 1, -94.1045, -9.99402, 1, -94.1045, -20.494, 1, -94.1045, -41.244, 1, -73.1045, -41.244, 1, -62.6045, -41.244, 1, -52.1045, -41.244, 1, -41.6045, -4.74402, 1, -11.1045, 28.006, 1, 8.1455, 42.256, 1, -23.3545, 42.256, 1, -29.8545, 42.256, 1, -87.8545, 42.256, 1, -94.1045, 31.756, 1, -94.1045, 35.756, 1, 8.1455, 42.256, 1, 2.6455, 42.256, 1, -3.8545, -30.994, 1, -94.1045, -41.244, 1, -94.1045, -41.244, 1, -83.6045, 5.25598, 1, 5.3955, 12.506, 1, 8.1455, 20.256, 1, 8.1455, 42.256, 1, -81.3545, 42.256, 1, -87.8545, 31.756, 1, -94.1045, 28.006, 1, 8.1455, 35.756, 1, 8.1455, 42.256, 1, -3.8545, 42.256, 1, -10.3545, 5.00598, 1, -5.6045, 5.25598, 1, 5.3955, 20.256, 1, 8.1455, 28.006, 1, 8.1455, 42.256, 1, -29.8545, 42.256, 1, -36.3545, 42.256, 1, -74.8545, 42.256, 1, -81.3545, 31.756, 1, -94.1045, -20.494, 1, -94.1045, -30.994, 1, -94.1045, -41.244, 1, -83.6045, -41.244, 1, -73.1045, 42.256, 1, -55.6045, 42.256, 1, -62.1045, 21.256, 1, -94.1045, 42.256, 1, -42.6045, 42.256, 1, -49.1045, 10.756, 1, -94.1045, 0.505981, 1, -94.1045, -41.244, 1, -52.1045, -4.74402, 1, -11.1045, 42.256, 1, -68.3545, 42.256, 1, -74.8545, 31.756, 1, -94.1045, 28.006, 1, 8.1455, 42.256, 1, -10.3545, 42.256, 1, -16.8545, -41.244, 1, -41.6045, -41.244, 1, -31.1045, -4.74402, 1, -11.1045, 42.256, 1, -68.3545, 31.756, 1, -94.1045, 21.256, 1, -94.1045, 42.256, 1, -62.1045, -41.244, 1, -9.8545, -6.99402, 1, -9.6045, -6.74402, 1, -11.3545, -41.244, 1, -20.6045, 28.006, 1, 8.1455, 42.256, 1, -16.8545, 42.256, 1, -23.3545, 0.505981, 1, -94.1045, -9.99402, 1, -94.1045, -41.244, 1, -62.6045, -41.244, 1, -52.1045, -7.49402, 1, 8.1455, -7.49402, 1, 6.6455, -9.49402, 1, 6.6455, -14.994, 1, 8.1455, -41.244, 1, 9.3955, -37.494, 1, 9.3955, -37.244, 1, 8.1455, -41.244, 1, -0.3545, -5.99402, 1, 4.3955, -5.99402, 1, -0.1045, -6.99402, 1, -0.3545, -9.74402, 1, 4.6455, -14.994, 1, 8.1455, -9.74402, 1, 4.6455, -6.99402, 1, -0.3545, -22.494, 1, 8.1455, -41.244, 1, -0.3545, -29.994, 1, 8.1455, -22.494, 1, 8.1455, -6.99402, 1, -0.3545, -6.99402, 1, -9.6045, -41.244, 1, -9.8545, -14.994, 1, 8.1455, -9.49402, 1, 6.6455, -9.74402, 1, 4.6455, -41.244, 1, -0.3545, -37.244, 1, 8.1455, -29.994, 1, 8.1455, 0.00598145, 5.5, 5.1455, 0.00598145, 5.5, -4.6045, -0.244019, 5.5, 5.1455, 0.505981, 5.5, 5.6455, 0.00598145, 5.5, 5.1455, -0.244019, 5.5, 5.1455, -8.74402, 5.5, 5.3955, 4.75598, 1, 8.1455, 4.75598, 1, 5.6455, 3.00598, 1, 5.3955, 1.50598, 1, 6.3955, -7.49402, 1, 6.6455, -7.49402, 1, 8.1455, -1.49402, 1, 8.1455, 1.50598, 1, 6.3955, 3.00598, 1, 5.3955, 3.00598, 1, -3.3545, 1.50598, 1, 4.6455, 1.50598, 1, 6.3955, 1.50598, 1, 6.3955, -1.49402, 1, 8.1455, 4.75598, 1, 8.1455, 3.00598, 1, -3.3545, 1.00598, 1, -3.3545, 1.00598, 1, 4.3955, 1.50598, 1, 4.6455, -1.24402, 1, -2.8545, -4.74402, 1, -2.8545, -4.74402, 1, -0.3545, -4.74402, 1, -0.3545, -5.99402, 1, -0.1045, -5.99402, 1, 4.3955, -1.24402, 1, 4.3955, -1.24402, 1, -2.8545, -4.74402, 1, -0.3545, -5.99402, 1, 4.3955, -42.244, 10.25, 8.3955, -42.494, 10.25, 8.1455, -42.494, 10.25, 18.8955, -42.244, 10.25, 19.1455, -21.244, 10.25, 95.1455, -20.744, 10.25, 94.8955, -31.494, 10.25, 94.8955, -31.994, 10.25, 95.1455, 43.506, 10.25, 18.8955, 43.506, 10.25, 8.1455, 43.256, 10.25, 8.3955, 43.256, 10.25, 19.1455, -20.744, 10.25, 94.8955, -21.244, 10.25, 95.1455, -10.494, 10.25, 95.1455, -9.99402, 10.25, 94.8955, -42.244, 10.25, 19.1455, -42.494, 10.25, 18.8955, -42.494, 10.25, 29.8955, -42.244, 10.25, 29.8955, -42.494, 10.25, 51.6455, -42.494, 10.25, 62.3955, -42.244, 10.25, 62.3955, -42.244, 10.25, 51.6455, -42.244, 10.25, 40.6455, -42.244, 10.25, 29.8955, -42.494, 10.25, 29.8955, -42.494, 10.25, 40.6455, -42.494, 10.25, 62.3955, -42.494, 10.25, 73.3955, -42.244, 10.25, 73.3955, -42.244, 10.25, 62.3955, 43.506, 10.25, 29.8955, 43.506, 10.25, 18.8955, 43.256, 10.25, 19.1455, 43.256, 10.25, 29.8955, -42.244, 10.25, 51.6455, -42.244, 10.25, 40.6455, -42.494, 10.25, 40.6455, -42.494, 10.25, 51.6455, 43.506, 10.25, 40.6455, 43.506, 10.25, 29.8955, 43.256, 10.25, 29.8955, 43.256, 10.25, 40.6455, -42.244, 10.25, 84.1455, -42.244, 10.25, 73.3955, -42.494, 10.25, 73.3955, -42.494, 10.25, 84.3955, 43.506, 10.25, 40.6455, 43.256, 10.25, 40.6455, 43.256, 10.25, 51.3955, 43.506, 10.25, 51.6455, -41.994, 10.25, 94.8955, -42.244, 10.25, 84.1455, -42.494, 10.25, 84.3955, -42.494, 10.25, 95.3955, 43.506, 10.25, 51.6455, 43.256, 10.25, 51.3955, 43.256, 10.25, 62.1455, 43.506, 10.25, 62.3955, -41.994, 10.25, 94.8955, -42.494, 10.25, 95.3955, -31.994, 10.25, 95.1455, -31.494, 10.25, 94.8955, 43.506, 10.25, 62.3955, 43.256, 10.25, 62.1455, 43.256, 10.25, 72.8955, 43.506, 10.25, 73.3955, -9.99402, 10.25, 94.8955, -10.494, 10.25, 95.1455, 0.505981, 10.25, 95.1455, 0.755981, 10.25, 94.8955, 43.506, 10.25, 73.3955, 43.256, 10.25, 72.8955, 43.256, 10.25, 83.6455, 43.506, 10.25, 84.3955, 11.256, 10.25, 95.1455, 22.006, 10.25, 95.1455, 22.006, 10.25, 94.8955, 11.256, 10.25, 94.8955, 43.506, 10.25, 84.3955, 43.256, 10.25, 83.6455, 43.256, 10.25, 94.6455, 43.506, 10.25, 95.3955, 22.006, 10.25, 95.1455, 32.756, 10.25, 95.1455, 32.756, 10.25, 94.8955, 22.006, 10.25, 94.8955, 32.756, 10.25, 95.1455, 43.506, 10.25, 95.3955, 43.256, 10.25, 94.6455, 32.756, 10.25, 94.8955, 11.256, 10.25, 95.1455, 11.256, 10.25, 94.8955, 0.755981, 10.25, 94.8955, 0.505981, 10.25, 95.1455, 26.006, 5.5, 9.3955, 35.006, 5.5, 9.3955, 26.256, 5.5, 9.1455, -27.744, 5.5, 9.1455, -36.494, 5.5, 9.1455, -27.994, 5.5, 9.3955, 26.006, 5.5, 9.3955, 26.256, 5.5, 9.1455, 17.256, 5.5, 9.1455, 17.006, 5.5, 9.3955, -27.744, 5.5, 9.1455, -27.994, 5.5, 9.3955, -18.994, 5.5, 9.3955, -18.744, 5.5, 9.1455, 17.006, 5.5, 9.3955, 17.256, 5.5, 9.1455, 8.25598, 5.5, 9.1455, 8.00598, 5.5, 9.3955, -18.744, 5.5, 9.1455, -18.994, 5.5, 9.3955, -9.99402, 5.5, 9.3955, -9.74402, 5.5, 9.1455, 8.00598, 5.5, 9.3955, 8.25598, 5.5, 9.1455, -0.744019, 5.5, 9.1455, -0.994019, 5.5, 9.3955, -9.74402, 5.5, 9.1455, -9.99402, 5.5, 9.3955, -0.994019, 5.5, 9.3955, -0.744019, 5.5, 9.1455, 42.256, 1, 9.1455, 36.006, 1, 9.1455, 35.756, 1, 10.3955, 42.256, 1, 20.8955, -2.24402, 1, 54.8955, -1.99402, 1, 56.6455, 42.256, 1, 56.3955, 42.256, 1, 44.3955, 42.256, 1, 20.8955, 35.756, 1, 10.3955, 25.756, 1, 10.3955, 42.256, 1, 32.6455, 42.256, 1, 20.8955, 25.756, 1, 10.3955, 15.756, 1, 10.3955, -4.24402, 1, 10.3955, -4.24402, 1, 54.8955, -2.24402, 1, 54.8955, 42.256, 1, 44.3955, 42.256, 1, 32.6455, 5.75598, 1, 10.3955, 42.256, 1, 32.6455, 15.756, 1, 10.3955, 5.75598, 1, 10.3955, -37.244, 1, 10.3955, -37.494, 1, 9.3955, -41.244, 1, 9.3955, -41.244, 1, 20.8955, -41.244, 1, 43.8955, -8.24402, 1, 54.8955, -6.24402, 1, 54.8955, -6.24402, 1, 10.3955, -13.994, 1, 10.3955, -41.244, 1, 32.3955, -29.494, 1, 10.3955, -37.244, 1, 10.3955, -41.244, 1, 20.8955, -21.744, 1, 10.3955, -29.494, 1, 10.3955, -41.244, 1, 20.8955, -13.994, 1, 10.3955, -21.744, 1, 10.3955, -41.244, 1, 20.8955, -41.244, 1, 32.3955, -41.244, 1, 55.3955, -8.24402, 1.25, 55.3955, -8.24402, 1, 54.8955, -41.244, 1, 43.8955, -6.24402, 1, 54.8955, -4.24402, 1, 54.8955, -4.24402, 1, 10.3955, -6.24402, 1, 10.3955, -8.24402, 1.25, 65.1455, -6.49402, 2.25, 65.1455, -6.49402, 2.25, 55.8955, -8.24402, 1.25, 55.8955, -41.244, 1, 65.6455, -8.24402, 1.25, 65.1455, -8.24402, 1.25, 55.8955, -41.244, 1, 55.3955, -41.244, 1, 65.6455, -8.24402, 1.25, 65.6455, -8.24402, 1.25, 65.1455, -8.24402, 1.25, 55.8955, -8.24402, 1.25, 55.3955, -41.244, 1, 55.3955, 0.00598145, 5, 55.8955, -6.49402, 2.25, 55.8955, -6.49402, 2.25, 65.1455, 0.00598145, 5, 65.1455, 0.00598145, 5, 65.1455, 6.75598, 5, 65.1455, 6.75598, 5, 55.8955, 0.00598145, 5, 55.8955, 12.006, 5, 55.8955, 12.006, 5, 65.1455, 18.506, 5, 65.1455, 18.506, 5, 55.8955, -1.99402, 1, 56.6455, -1.99402, 1, 64.3955, 42.256, 1, 64.6455, 42.256, 1, 56.3955, 30.756, 1, 93.8955, 42.256, 1, 93.8955, 42.256, 1, 86.3955, 30.756, 1, 93.8955, 42.256, 1, 86.3955, 42.256, 1, 79.1455, 30.756, 1, 93.8955, 42.256, 1, 79.1455, 42.256, 1, 71.8955, -2.24402, 1, 66.1455, -3.74402, 1, 66.1455, -3.49402, 1, 93.8955, 7.75598, 1, 93.8955, 30.756, 1, 93.8955, 42.256, 1, 71.8955, 42.256, 1, 64.6455, -1.99402, 1, 64.3955, 19.256, 1, 93.8955, -1.99402, 1, 64.3955, -2.24402, 1, 66.1455, 7.75598, 1, 93.8955, 19.256, 1, 93.8955, -41.244, 1, 86.6455, -41.244, 1, 93.8955, -32.744, 1, 93.8955, -41.244, 1, 79.6455, -41.244, 1, 86.6455, -32.744, 1, 93.8955, -41.244, 1, 79.6455, -32.744, 1, 93.8955, -24.244, 1, 93.8955, -41.244, 1, 72.6455, -15.744, 1, 93.8955, -8.24402, 1, 66.1455, -8.24402, 1.25, 65.6455, -41.244, 1, 65.6455, -41.244, 1, 72.6455, -24.244, 1, 93.8955, -6.99402, 1, 93.8955, -6.74402, 1, 66.1455, -8.24402, 1, 66.1455, -15.744, 1, 93.8955, -6.99402, 1, 93.8955, -3.49402, 1, 93.8955, -3.74402, 1, 66.1455, -6.74402, 1, 66.1455)
polygons = [PackedInt32Array(0, 2, 1), PackedInt32Array(5, 4, 3), PackedInt32Array(8, 7, 6), PackedInt32Array(11, 10, 9), PackedInt32Array(14, 13, 15), PackedInt32Array(15, 13, 12), PackedInt32Array(16, 19, 17), PackedInt32Array(17, 19, 18), PackedInt32Array(22, 21, 20), PackedInt32Array(25, 24, 23), PackedInt32Array(26, 29, 27), PackedInt32Array(27, 29, 28), PackedInt32Array(31, 30, 32), PackedInt32Array(32, 30, 33), PackedInt32Array(36, 35, 34), PackedInt32Array(39, 38, 37), PackedInt32Array(42, 41, 40), PackedInt32Array(45, 44, 43), PackedInt32Array(48, 47, 46), PackedInt32Array(51, 50, 49), PackedInt32Array(54, 53, 52), PackedInt32Array(57, 56, 55), PackedInt32Array(58, 61, 59), PackedInt32Array(59, 61, 60), PackedInt32Array(64, 63, 65), PackedInt32Array(65, 63, 62), PackedInt32Array(68, 67, 69), PackedInt32Array(69, 67, 66), PackedInt32Array(70, 73, 71), PackedInt32Array(71, 73, 72), PackedInt32Array(76, 75, 77), PackedInt32Array(77, 75, 74), PackedInt32Array(79, 78, 80), PackedInt32Array(80, 78, 81), PackedInt32Array(87, 86, 82), PackedInt32Array(82, 86, 83), PackedInt32Array(83, 86, 84), PackedInt32Array(84, 86, 85), PackedInt32Array(89, 88, 90), PackedInt32Array(90, 88, 91), PackedInt32Array(94, 93, 92), PackedInt32Array(97, 96, 95), PackedInt32Array(100, 99, 98), PackedInt32Array(103, 102, 101), PackedInt32Array(106, 105, 104), PackedInt32Array(108, 107, 109), PackedInt32Array(109, 107, 110), PackedInt32Array(113, 112, 111), PackedInt32Array(115, 114, 116), PackedInt32Array(116, 114, 117), PackedInt32Array(121, 120, 118), PackedInt32Array(118, 120, 119), PackedInt32Array(123, 122, 124), PackedInt32Array(124, 122, 125), PackedInt32Array(128, 127, 126), PackedInt32Array(130, 129, 131), PackedInt32Array(131, 129, 132), PackedInt32Array(134, 133, 135), PackedInt32Array(135, 133, 138), PackedInt32Array(135, 138, 136), PackedInt32Array(136, 138, 137), PackedInt32Array(140, 139, 141), PackedInt32Array(141, 139, 142), PackedInt32Array(144, 143, 145), PackedInt32Array(145, 143, 146), PackedInt32Array(148, 147, 149), PackedInt32Array(149, 147, 150), PackedInt32Array(153, 152, 151), PackedInt32Array(156, 155, 154), PackedInt32Array(159, 158, 157), PackedInt32Array(162, 161, 160), PackedInt32Array(165, 164, 163), PackedInt32Array(168, 167, 166), PackedInt32Array(171, 170, 169), PackedInt32Array(174, 173, 175), PackedInt32Array(175, 173, 172), PackedInt32Array(178, 177, 179), PackedInt32Array(179, 177, 176), PackedInt32Array(179, 176, 180), PackedInt32Array(180, 176, 181), PackedInt32Array(184, 183, 182), PackedInt32Array(186, 185, 187), PackedInt32Array(187, 185, 188), PackedInt32Array(191, 190, 189), PackedInt32Array(193, 192, 194), PackedInt32Array(194, 192, 195), PackedInt32Array(195, 192, 197), PackedInt32Array(195, 197, 196), PackedInt32Array(200, 199, 198), PackedInt32Array(201, 203, 202), PackedInt32Array(206, 205, 204), PackedInt32Array(207, 210, 208), PackedInt32Array(208, 210, 209), PackedInt32Array(212, 211, 213), PackedInt32Array(213, 211, 214), PackedInt32Array(217, 216, 215), PackedInt32Array(219, 218, 220), PackedInt32Array(220, 218, 221), PackedInt32Array(223, 222, 224), PackedInt32Array(224, 222, 225), PackedInt32Array(227, 226, 228), PackedInt32Array(228, 226, 229), PackedInt32Array(231, 230, 232), PackedInt32Array(232, 230, 233), PackedInt32Array(235, 234, 236), PackedInt32Array(236, 234, 237), PackedInt32Array(239, 238, 240), PackedInt32Array(240, 238, 243), PackedInt32Array(240, 243, 241), PackedInt32Array(241, 243, 242), PackedInt32Array(244, 246, 245), PackedInt32Array(249, 248, 247), PackedInt32Array(252, 251, 250), PackedInt32Array(254, 253, 255), PackedInt32Array(255, 253, 256), PackedInt32Array(259, 258, 260), PackedInt32Array(260, 258, 257), PackedInt32Array(262, 261, 263), PackedInt32Array(263, 261, 264), PackedInt32Array(268, 267, 265), PackedInt32Array(265, 267, 266), PackedInt32Array(269, 271, 270), PackedInt32Array(274, 273, 275), PackedInt32Array(275, 273, 272), PackedInt32Array(278, 277, 276), PackedInt32Array(281, 280, 279), PackedInt32Array(285, 284, 282), PackedInt32Array(282, 284, 283), PackedInt32Array(289, 288, 286), PackedInt32Array(286, 288, 287), PackedInt32Array(293, 292, 290), PackedInt32Array(290, 292, 291), PackedInt32Array(297, 296, 294), PackedInt32Array(294, 296, 295), PackedInt32Array(301, 300, 298), PackedInt32Array(298, 300, 299), PackedInt32Array(305, 304, 302), PackedInt32Array(302, 304, 303), PackedInt32Array(309, 308, 306), PackedInt32Array(306, 308, 307), PackedInt32Array(313, 312, 310), PackedInt32Array(310, 312, 311), PackedInt32Array(317, 316, 314), PackedInt32Array(314, 316, 315), PackedInt32Array(321, 320, 318), PackedInt32Array(318, 320, 319), PackedInt32Array(325, 324, 322), PackedInt32Array(322, 324, 323), PackedInt32Array(329, 328, 326), PackedInt32Array(326, 328, 327), PackedInt32Array(331, 330, 332), PackedInt32Array(332, 330, 333), PackedInt32Array(335, 334, 336), PackedInt32Array(336, 334, 337), PackedInt32Array(339, 338, 340), PackedInt32Array(340, 338, 341), PackedInt32Array(345, 344, 342), PackedInt32Array(342, 344, 343), PackedInt32Array(349, 348, 346), PackedInt32Array(346, 348, 347), PackedInt32Array(351, 350, 352), PackedInt32Array(352, 350, 353), PackedInt32Array(357, 356, 354), PackedInt32Array(354, 356, 355), PackedInt32Array(359, 358, 360), PackedInt32Array(360, 358, 361), PackedInt32Array(365, 364, 362), PackedInt32Array(362, 364, 363), PackedInt32Array(369, 368, 366), PackedInt32Array(366, 368, 367), PackedInt32Array(373, 372, 370), PackedInt32Array(370, 372, 371), PackedInt32Array(377, 376, 374), PackedInt32Array(374, 376, 375), PackedInt32Array(379, 378, 380), PackedInt32Array(380, 378, 381), PackedInt32Array(384, 383, 382), PackedInt32Array(387, 386, 385), PackedInt32Array(391, 390, 388), PackedInt32Array(388, 390, 389), PackedInt32Array(395, 394, 392), PackedInt32Array(392, 394, 393), PackedInt32Array(399, 398, 396), PackedInt32Array(396, 398, 397), PackedInt32Array(403, 402, 400), PackedInt32Array(400, 402, 401), PackedInt32Array(407, 406, 404), PackedInt32Array(404, 406, 405), PackedInt32Array(411, 410, 408), PackedInt32Array(408, 410, 409), PackedInt32Array(413, 412, 414), PackedInt32Array(414, 412, 415), PackedInt32Array(417, 416, 418), PackedInt32Array(418, 416, 419), PackedInt32Array(422, 421, 420), PackedInt32Array(425, 424, 426), PackedInt32Array(426, 424, 423), PackedInt32Array(428, 427, 429), PackedInt32Array(429, 427, 432), PackedInt32Array(429, 432, 431), PackedInt32Array(429, 431, 430), PackedInt32Array(435, 434, 433), PackedInt32Array(437, 436, 438), PackedInt32Array(438, 436, 439), PackedInt32Array(441, 440, 442), PackedInt32Array(442, 440, 445), PackedInt32Array(442, 445, 444), PackedInt32Array(442, 444, 443), PackedInt32Array(448, 447, 446), PackedInt32Array(451, 450, 449), PackedInt32Array(453, 452, 454), PackedInt32Array(454, 452, 455), PackedInt32Array(457, 456, 458), PackedInt32Array(458, 456, 459), PackedInt32Array(463, 462, 460), PackedInt32Array(460, 462, 461), PackedInt32Array(467, 466, 464), PackedInt32Array(464, 466, 465), PackedInt32Array(469, 468, 470), PackedInt32Array(470, 468, 471), PackedInt32Array(474, 473, 472), PackedInt32Array(477, 476, 475), PackedInt32Array(481, 480, 478), PackedInt32Array(478, 480, 479), PackedInt32Array(485, 484, 482), PackedInt32Array(482, 484, 483), PackedInt32Array(489, 488, 486), PackedInt32Array(486, 488, 487), PackedInt32Array(490, 493, 491), PackedInt32Array(491, 493, 492), PackedInt32Array(496, 495, 494), PackedInt32Array(499, 498, 497), PackedInt32Array(502, 501, 500), PackedInt32Array(504, 503, 505), PackedInt32Array(505, 503, 506), PackedInt32Array(508, 507, 509), PackedInt32Array(509, 507, 511), PackedInt32Array(509, 511, 510), PackedInt32Array(513, 512, 514), PackedInt32Array(514, 512, 515), PackedInt32Array(518, 517, 516), PackedInt32Array(521, 520, 519), PackedInt32Array(522, 525, 523), PackedInt32Array(523, 525, 524), PackedInt32Array(527, 526, 528), PackedInt32Array(528, 526, 531), PackedInt32Array(528, 531, 530), PackedInt32Array(528, 530, 529), PackedInt32Array(533, 532, 534), PackedInt32Array(534, 532, 535), PackedInt32Array(539, 538, 536), PackedInt32Array(536, 538, 537)]

[sub_resource type="ViewportTexture" id="ViewportTexture_wodat"]
viewport_path = NodePath("ExternalCameras/Node3D/SubViewport2")

[sub_resource type="ViewportTexture" id="ViewportTexture_p8nu4"]
viewport_path = NodePath("ExternalCameras/Node3D2/SubViewport")

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_o8ecy"]
_data = PackedByteArray("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")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_nwt2r"]
resource_name = "Texxture"
cull_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_o8ecy")
roughness = 0.5

[sub_resource type="ArrayMesh" id="ArrayMesh_685ns"]
resource_name = "waterwierd1_waterwierd_waterwierd"
_blend_shape_names = PackedStringArray("sk_000", "sk_001", "sk_002", "sk_003", "sk_004", "sk_005", "sk_006", "sk_007", "sk_008", "sk_009", "sk_010", "sk_011", "sk_012", "sk_013", "sk_014", "sk_015", "sk_016", "sk_017", "sk_018", "sk_019", "sk_020", "sk_021", "sk_022", "sk_023", "sk_024", "sk_025", "sk_026", "sk_027", "sk_028", "sk_029", "sk_030", "sk_031", "sk_032", "sk_033", "sk_034", "sk_035", "sk_036", "sk_037", "sk_038", "sk_039", "sk_040", "sk_041", "sk_042", "sk_043", "sk_044", "sk_045", "sk_046", "sk_047", "sk_048", "sk_049", "sk_050", "sk_051", "sk_052", "sk_053", "sk_054", "sk_055", "sk_056", "sk_057", "sk_058", "sk_059", "sk_060", "sk_061", "sk_062", "sk_063", "sk_064", "sk_065")
_surfaces = [{
"aabb": AABB(-50.0847, -2.41761, -52.8572, 106.021, 78.6484, 106.646),
"attribute_data": PackedByteArray("AAAUPwAAyD4AAAAAAAB4PwAAUD4AAHg/AACAPQAAeD8AADQ/AAB4PwAA6D4AAHg/AABAPgAAeD8AAEA9AAB4PwAAfD8AAHg/AABgPwAAeD8AADg/AAB4PwAAAD8AAHg/AABsPwAAeD8AAKg+AAAoPwAAoD4AACg/AAAYPwAA+D4AAMg+AACgPgAA0D4AAGA+AADIPgAAAD4AAAw/AAAAPgAAKD8AAFQ/AACgPgAAVD8AAFw/AABUPwAAJD8AADw/AACYPgAAVD8AADA/AADIPgAAmD4AAMg+AADAPgAAyD4AAMA+AABgPgAACD8AAGA+AAAIPwAAYD4AAAw/AAAAPgAAMD8AAGg/AABwPwAAaD8AADQ/AABoPwAAiD4AAGg/AACAPgAAaD8AAMA9AABoPwAAID4AAFQ/AAAwPgAAPD8AACw/AABUPwAAmD4AADw/AABUPwAAPD8AACQ/AAA8PwAAoD4AADw/AABMPwAAKD8AACA/AAAoPwAAUD4AACg/AAAgPwAAKD8AAKg+AAAQPwAAcD4AABA/AAAcPwAAED8AAEQ/AAAQPwAAHD8AABA/AACwPgAAED8AABg/AAD4PgAAsD4AAPg+AACAPgAA+D4AALg+AAD4PgAAPD8AAPg+AADAPgAAyD4AABQ/AADIPgAAED8AAKA+AAAoPwAAoD4AABA/AACgPgAAED8AAKA+AADIPgAAoD4AABA/AACgPgAAqD4AAKA+AADYPgAAYD4AABA/AACgPgAA+D4AAAAAAAAYPwAAYD4AABA/AACgPgAA+D4AAAAAAAD4PgAAAAA="),
"blend_shapes": PackedByteArray("CdNNwTG5VUI+vgFAJrgMwTEUa0CAUwm+aDOgwDEUa0Bh6v9Av5/3wDEUa0CnR3lAoiCCQDEUa0Bh6v9ApuYZvzEUa0Av4hNBnUmuwDEUa0Cu5wHBonIBwTEUa0Ch8ZDAzkQUQTEUa0CAUwm+qVztQDEUa0CVzZ9AWEuQQDEUa0Cu5wHBmFWPPjEUa0C8ABfBhVsDQTEUa0C/SZPAhYRSwAGf/0EQYolAsBUdwadr8EG95me/affewGixPEJVRH6/9hXIwLV1akIw1itAUcySwHguf0J/IclAha8LQJ04hkKULa1Aa8qZP6J0iULGeddAPbovQKXDhUFj73NA3f0twCeAgUFAMp1AcmCeQB7Xh0EECOG/5d4ywKJrwEEQprnAP6yawJZSfkG+NJ/Aj644wX3NVUITggq/Xa8IwWRwUkIotZlA8Wk0wWyPU0KPMJxAE0x4wLQXfkJRlKNA79WjwIM9gUK/zDVAcXi6wJ/AgUKFIrRAAOOZPyYEikKCGZFAcQZMQDeBDUFmNrBAtdTkQOliDUF+2hK/NStXQM/pCEHolNLAQzZmwOIKCUE0R7BAvgN3wPR/BEEahNLAEVvNwHWaBEERVBK/gSfCwDNKekHyr70+MNbSwBbVtUFeWfs/yvpkPxF8g0FNb8LAqKPmwLWytUE6I0HAjYr7P5U2y0Eld0/Ahf/kPyAeyUGk2hRA3lMcwL9dvkEFm5xA8igawJD5AkJu9IjApCglv24WBEL0mw4/F6XzwNKl8kE81ExAAKniwOUF+UH1gpvAsqQ3wZqQFUIVjaU/iakHwRzSGEJ2dXtAhPYqwUvVGEKtATTA137fwA00HkIxiHrAFEaCwJRhIEJRVQu/4uSYwI8lHULQcFpADWhPwXxMN0L3FPK+ha9CwX8oNkKNiVVABBsNwfJCN0IKH5BAWIbFwOKDOkKd2ipAAX4mwWuUOkIrlzTAx2PhwCiHU0IY7iFApLIIwXSuVELGj9u9E00Awf+5bUKRLG4/my4iwQfZbkKT28I/mgclwf+5bULfzoJAmgclwf+5bULfzoJAIl4HwTSEa0IzP71Amgclwf+5bULfzoJAoz7WwHBtakJn/KBAXMWBwLw2f0K+OVtAmgclwf+5bULfzoJAnKQXQcGXiELk2NJAA77FwCRQgkJvv3dAmgclwf+5bULfzoJAnKQXQcGXiELk2NJAnKQXQcGXiELk2NJA2uteaPEKH/+91StRZwps/1PajiXByu4/aNnBPcOnRT/WIjUg7/8iVofPhAHn/B8/0qaiX9b/8KVRv6FZ8v/EkuEjYVYy9T962yIpN+r/52XcWpljQNACwK19bFf1+erA+kMFXyiur8CwDToc8ORiJBffcJlU9Qz9hUUAaboA4WN8Lz+SBBCVDTITD/I0P1LWY1iYy5oOTQNa0uIG////v8UPIzOL+Egg9/pYIf7VsjqpKLtoXAkvd82DM3i25FnOe73jgBX4BuFmpq5oYTIrRKUJlthDLxnD1v+czisMctwtNzzHWPabdqt+vVp9Ov0qJNfrKEYeLO30fbQ+hjzgMCUeSyMY/gMoTSWMWqntI3z8W0JlU8+7wRPjsSeOyx0+hqdsZhP+C9l74Rpg2/Pi/m75qJuSAfn0zfDIumoHzJZRZEFy59crxQjLgo9M863t+z4uaA0LSGvJFUJB1fF+FPH6WgMi42Eyn1VOZ6ITY2LkIhJQS/U0BmHy7M5nDEKgoKYbgrjmm9xk7OauAwDg78r29OSLFLKoC7/+hnbpY+cWbU1qrRgLWHg0vV/5A8MNlxFgMvXpDRtM3wKC3PBf8m/**************************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"),
"format": 34359742487,
"index_count": 378,
"index_data": PackedByteArray("FAAgACEAIAAJACEACQAIACEABAAJACAACAAMACEABQAEACAAIgAhAAwADAAKACIACgALACIAIwAFACAAAgAFACMAIwADAAIAIgALACQACwAGACQABgAHACQAJAAHACUAJQADACMABwABACUAAQADACUAFQAjACAAJgAlACMAJgAjABUAFQAgABQAJgAYACUAGAAkACUAJwAmABUAGAAoACQAKAAiACQAKQAYACYAJwApACYAFwAoABgAKQAXABgAFgAiACgAFgAhACIAFAAhABYAKgAWACgAKgAoABcAKwAUABYAKwAWACoALAAVABQALAAUACsAJwAVACwALQAqABcALgArACoALgAqAC0ADQAsACsADQArAC4ALwAnACwALwAsAA0ALwAOACcADgApACcAMAAXACkADgAwACkALQAXADAAMQAOAC8AMgAvAA0AMgAxAC8AMwAwAA4AMQAzAA4ANAAtADAANAAwADMANQAtADQANQAuAC0ANgANAC4ANgAuADUAMgANADYANwAzADEAOAAxADIAOAA3ADEAOQAyADYAOQA4ADIAOgA2ADUAOQA2ADoAOgA1AA8ADwA1ADQAOwA0ADMADwA0ADsAOwAzADcAPAA6AA8APQAPADsAPAAPAD0AGQA7ADcAPQA7ABkAGQA3AAAAAAA3ADgAPgA9ABkAPwAZAAAAPgAZAD8APwAAAEAAGwAAADgAQQAAABsAGwA4ADkAEAA9AD4AEAA8AD0AGgAbADkAGgA5ADoAGgA6ADwAQgAbABoAQgBDABsARAAaADwARABCABoARAA8ABAAEQBCAEQAHABEABAAHAARAEQAHAAQAEUARQAQAD4AEQAcABIAHABFABIAEQAeAEIAHgBGAEIAEgATABEAHgARABMAEwASAEcASAAeABMASABJAB4AHwBIABMAHwATAEoAEgBFAB8AEgAfAEsARQAdAB8AHQBIAB8ARQA+AB0AHQA+AD8AHQA/AEgASAA/AEMA"),
"lods": [2.35163, PackedByteArray("FAAEAAgABAAJAAgAFQAEABQAAgAFAAQAFQACAAQAFAAIABYADQAVABQADQAUABYAFgAIAAoACgAIAAwADQAWAA8AFgAKABcADwAWABcAFwAKAAYACgALAAYAGAAXAAYAGAAGAAEABgAHAAEADgAXABgADgAYAAEADwAXAA4AAQADAAIADgABAAIADgACABUAGQAPAA4AGgAVAA0AGgANAA8AEAAPABkAEAAaAA8AGQAOAAAAEAAZAAAAGwAOABUAGgAbABUAAAAOABsAHAAaABAAHAAbABoAHQAQAAAAHAAQAB0AHgAAABsAHAAeABsAHQAAAB4AHgAcABMAHAAdABIAEgATABwAHQAeAB8AEgAdAB8AHwAeABMAHwATABIA"), 3.03562, PackedByteArray("DQAEAAgABAAJAAgAAgAFAAQADQACAAQAAQADAAIADgABAAIADgACAA0ABgAHAAEADgAGAAEADgAKAAYACgALAAYACgAIAAwADwAIAAoADwAKAA4ADQAIAA8ADwAOAAAAAAAOAA0AEAANAA8AEAAAAA0AEAAPAAAAEQAQAAAAEQAAABAAEgATABEAEgARABMA"), 4.14456, PackedByteArray("AAABAAIAAQADAAIAAAACAAQAAgAFAAQAAAAGAAEABgAHAAEABAAIAAAABAAJAAgAAAAKAAYAAAAIAAoACgALAAYACgAIAAwA")],
"material": SubResource("StandardMaterial3D_nwt2r"),
"name": "Texxture",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 76,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
metadata/extras = {
"targetNames": ["sk_000", "sk_001", "sk_002", "sk_003", "sk_004", "sk_005", "sk_006", "sk_007", "sk_008", "sk_009", "sk_010", "sk_011", "sk_012", "sk_013", "sk_014", "sk_015", "sk_016", "sk_017", "sk_018", "sk_019", "sk_020", "sk_021", "sk_022", "sk_023", "sk_024", "sk_025", "sk_026", "sk_027", "sk_028", "sk_029", "sk_030", "sk_031", "sk_032", "sk_033", "sk_034", "sk_035", "sk_036", "sk_037", "sk_038", "sk_039", "sk_040", "sk_041", "sk_042", "sk_043", "sk_044", "sk_045", "sk_046", "sk_047", "sk_048", "sk_049", "sk_050", "sk_051", "sk_052", "sk_053", "sk_054", "sk_055", "sk_056", "sk_057", "sk_058", "sk_059", "sk_060", "sk_061", "sk_062", "sk_063", "sk_064", "sk_065"]
}

[sub_resource type="Animation" id="Animation_g0q4o"]
resource_name = "waterwierd_waterwierdAction"
length = 22.0
tracks/0/type = "blend_shape"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("Waterwierd:sk_000")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 1, 0.0333333, 1, 1, 0.366667, 1, 0, 22, 1, 0)
tracks/1/type = "blend_shape"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("Waterwierd:sk_001")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, 0, 0.0333333, 1, 0, 0.366667, 1, 1, 0.7, 1, 0, 22, 1, 0)
tracks/2/type = "blend_shape"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Waterwierd:sk_002")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 0, 0.366667, 1, 0, 0.7, 1, 1, 1.03333, 1, 0, 22, 1, 0)
tracks/3/type = "blend_shape"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Waterwierd:sk_003")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, 0, 0.7, 1, 0, 1.03333, 1, 1, 1.36667, 1, 0, 22, 1, 0)
tracks/4/type = "blend_shape"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Waterwierd:sk_004")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0, 1.03333, 1, 0, 1.36667, 1, 1, 1.7, 1, 0, 22, 1, 0)
tracks/5/type = "blend_shape"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Waterwierd:sk_005")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 0, 1.36667, 1, 0, 1.7, 1, 1, 2.03333, 1, 0, 22, 1, 0)
tracks/6/type = "blend_shape"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Waterwierd:sk_006")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0, 1.7, 1, 0, 2.03333, 1, 1, 2.36667, 1, 0, 22, 1, 0)
tracks/7/type = "blend_shape"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Waterwierd:sk_007")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0, 2.03333, 1, 0, 2.36667, 1, 1, 2.7, 1, 0, 22, 1, 0)
tracks/8/type = "blend_shape"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Waterwierd:sk_008")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0, 2.36667, 1, 0, 2.7, 1, 1, 3.03333, 1, 0, 22, 1, 0)
tracks/9/type = "blend_shape"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Waterwierd:sk_009")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 0, 2.7, 1, 0, 3.03333, 1, 1, 3.36667, 1, 0, 22, 1, 0)
tracks/10/type = "blend_shape"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Waterwierd:sk_010")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, 0, 3.03333, 1, 0, 3.36667, 1, 1, 3.7, 1, 0, 22, 1, 0)
tracks/11/type = "blend_shape"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Waterwierd:sk_011")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0, 3.36667, 1, 0, 3.7, 1, 1, 4.03333, 1, 0, 22, 1, 0)
tracks/12/type = "blend_shape"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Waterwierd:sk_012")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 0, 3.7, 1, 0, 4.03333, 1, 1, 4.36667, 1, 0, 22, 1, 0)
tracks/13/type = "blend_shape"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Waterwierd:sk_013")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, 0, 4.03333, 1, 0, 4.36667, 1, 1, 4.7, 1, 0, 22, 1, 0)
tracks/14/type = "blend_shape"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Waterwierd:sk_014")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 0, 4.36667, 1, 0, 4.7, 1, 1, 5.03333, 1, 0, 22, 1, 0)
tracks/15/type = "blend_shape"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Waterwierd:sk_015")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 0, 4.7, 1, 0, 5.03333, 1, 1, 5.36667, 1, 0, 22, 1, 0)
tracks/16/type = "blend_shape"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Waterwierd:sk_016")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, 0, 5.03333, 1, 0, 5.36667, 1, 1, 5.7, 1, 0, 22, 1, 0)
tracks/17/type = "blend_shape"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Waterwierd:sk_017")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 0, 5.36667, 1, 0, 5.7, 1, 1, 6.03333, 1, 0, 22, 1, 0)
tracks/18/type = "blend_shape"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Waterwierd:sk_018")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, 0, 5.7, 1, 0, 6.03333, 1, 1, 6.36667, 1, 0, 22, 1, 0)
tracks/19/type = "blend_shape"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Waterwierd:sk_019")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, 0, 6.03333, 1, 0, 6.36667, 1, 1, 6.7, 1, 0, 22, 1, 0)
tracks/20/type = "blend_shape"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Waterwierd:sk_020")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 0, 6.36667, 1, 0, 6.7, 1, 1, 7.03333, 1, 0, 22, 1, 0)
tracks/21/type = "blend_shape"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Waterwierd:sk_021")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, 0, 6.7, 1, 0, 7.03333, 1, 1, 7.36667, 1, 0, 22, 1, 0)
tracks/22/type = "blend_shape"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Waterwierd:sk_022")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, 0, 7.03333, 1, 0, 7.36667, 1, 1, 7.7, 1, 0, 22, 1, 0)
tracks/23/type = "blend_shape"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Waterwierd:sk_023")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 0, 7.36667, 1, 0, 7.7, 1, 1, 8.03333, 1, 0, 22, 1, 0)
tracks/24/type = "blend_shape"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Waterwierd:sk_024")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, 0, 7.7, 1, 0, 8.03333, 1, 1, 8.36667, 1, 0, 22, 1, 0)
tracks/25/type = "blend_shape"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Waterwierd:sk_025")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 0, 8.03333, 1, 0, 8.36667, 1, 1, 8.7, 1, 0, 22, 1, 0)
tracks/26/type = "blend_shape"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Waterwierd:sk_026")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0, 8.36667, 1, 0, 8.7, 1, 1, 9.03333, 1, 0, 22, 1, 0)
tracks/27/type = "blend_shape"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Waterwierd:sk_027")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0, 8.7, 1, 0, 9.03333, 1, 1, 9.36667, 1, 0, 22, 1, 0)
tracks/28/type = "blend_shape"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Waterwierd:sk_028")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0, 9.03333, 1, 0, 9.36667, 1, 1, 9.7, 1, 0, 22, 1, 0)
tracks/29/type = "blend_shape"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Waterwierd:sk_029")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 0, 9.36667, 1, 0, 9.7, 1, 1, 10.0333, 1, 0, 22, 1, 0)
tracks/30/type = "blend_shape"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Waterwierd:sk_030")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0, 9.7, 1, 0, 10.0333, 1, 1, 10.3667, 1, 0, 22, 1, 0)
tracks/31/type = "blend_shape"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Waterwierd:sk_031")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, 0, 10.0333, 1, 0, 10.3667, 1, 1, 10.7, 1, 0, 22, 1, 0)
tracks/32/type = "blend_shape"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Waterwierd:sk_032")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 0, 10.3667, 1, 0, 10.7, 1, 1, 11.0333, 1, 0, 22, 1, 0)
tracks/33/type = "blend_shape"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Waterwierd:sk_033")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0, 10.7, 1, 0, 11.0333, 1, 1, 11.3667, 1, 0, 22, 1, 0)
tracks/34/type = "blend_shape"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Waterwierd:sk_034")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, 0, 11.0333, 1, 0, 11.3667, 1, 1, 11.7, 1, 0, 22, 1, 0)
tracks/35/type = "blend_shape"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Waterwierd:sk_035")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 0, 11.3667, 1, 0, 11.7, 1, 1, 12.0333, 1, 0, 22, 1, 0)
tracks/36/type = "blend_shape"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Waterwierd:sk_036")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, 0, 11.7, 1, 0, 12.0333, 1, 1, 12.3667, 1, 0, 22, 1, 0)
tracks/37/type = "blend_shape"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Waterwierd:sk_037")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, 0, 12.0333, 1, 0, 12.3667, 1, 1, 12.7, 1, 0, 22, 1, 0)
tracks/38/type = "blend_shape"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Waterwierd:sk_038")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 0, 12.3667, 1, 0, 12.7, 1, 1, 13.0333, 1, 0, 22, 1, 0)
tracks/39/type = "blend_shape"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Waterwierd:sk_039")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, 0, 12.7, 1, 0, 13.0333, 1, 1, 13.3667, 1, 0, 22, 1, 0)
tracks/40/type = "blend_shape"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Waterwierd:sk_040")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, 0, 13.0333, 1, 0, 13.3667, 1, 1, 13.7, 1, 0, 22, 1, 0)
tracks/41/type = "blend_shape"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Waterwierd:sk_041")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 0, 13.3667, 1, 0, 13.7, 1, 1, 14.0333, 1, 0, 22, 1, 0)
tracks/42/type = "blend_shape"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("Waterwierd:sk_042")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, 0, 13.7, 1, 0, 14.0333, 1, 1, 14.3667, 1, 0, 22, 1, 0)
tracks/43/type = "blend_shape"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("Waterwierd:sk_043")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, 0, 14.0333, 1, 0, 14.3667, 1, 1, 14.7, 1, 0, 22, 1, 0)
tracks/44/type = "blend_shape"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("Waterwierd:sk_044")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, 0, 14.3667, 1, 0, 14.7, 1, 1, 15.0333, 1, 0, 22, 1, 0)
tracks/45/type = "blend_shape"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("Waterwierd:sk_045")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0, 14.7, 1, 0, 15.0333, 1, 1, 15.3667, 1, 0, 22, 1, 0)
tracks/46/type = "blend_shape"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("Waterwierd:sk_046")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, 0, 15.0333, 1, 0, 15.3667, 1, 1, 15.7, 1, 0, 22, 1, 0)
tracks/47/type = "blend_shape"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("Waterwierd:sk_047")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 0, 15.3667, 1, 0, 15.7, 1, 1, 16.0333, 1, 0, 22, 1, 0)
tracks/48/type = "blend_shape"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("Waterwierd:sk_048")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, 0, 15.7, 1, 0, 16.0333, 1, 1, 16.3667, 1, 0, 22, 1, 0)
tracks/49/type = "blend_shape"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("Waterwierd:sk_049")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, 0, 16.0333, 1, 0, 16.3667, 1, 1, 16.7, 1, 0, 22, 1, 0)
tracks/50/type = "blend_shape"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("Waterwierd:sk_050")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array(0, 1, 0, 16.3667, 1, 0, 16.7, 1, 1, 17.0333, 1, 0, 22, 1, 0)
tracks/51/type = "blend_shape"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("Waterwierd:sk_051")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0, 16.7, 1, 0, 17.0333, 1, 1, 17.3667, 1, 0, 22, 1, 0)
tracks/52/type = "blend_shape"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("Waterwierd:sk_052")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array(0, 1, 0, 17.0333, 1, 0, 17.3667, 1, 1, 17.7, 1, 0, 22, 1, 0)
tracks/53/type = "blend_shape"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("Waterwierd:sk_053")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, 0, 17.3667, 1, 0, 17.7, 1, 1, 18.0333, 1, 0, 22, 1, 0)
tracks/54/type = "blend_shape"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("Waterwierd:sk_054")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, 0, 17.7, 1, 0, 18.0333, 1, 1, 18.3667, 1, 0, 22, 1, 0)
tracks/55/type = "blend_shape"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("Waterwierd:sk_055")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array(0, 1, 0, 18.0333, 1, 0, 18.3667, 1, 1, 18.7, 1, 0, 22, 1, 0)
tracks/56/type = "blend_shape"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("Waterwierd:sk_056")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array(0, 1, 0, 18.3667, 1, 0, 18.7, 1, 1, 19.0333, 1, 0, 22, 1, 0)
tracks/57/type = "blend_shape"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("Waterwierd:sk_057")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array(0, 1, 0, 18.7, 1, 0, 19.0333, 1, 1, 19.3667, 1, 0, 22, 1, 0)
tracks/58/type = "blend_shape"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("Waterwierd:sk_058")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array(0, 1, 0, 19.0333, 1, 0, 19.3667, 1, 1, 19.7, 1, 0, 22, 1, 0)
tracks/59/type = "blend_shape"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("Waterwierd:sk_059")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array(0, 1, 0, 19.3667, 1, 0, 19.7, 1, 1, 20.0333, 1, 0, 22, 1, 0)
tracks/60/type = "blend_shape"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("Waterwierd:sk_060")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array(0, 1, 0, 19.7, 1, 0, 20.0333, 1, 1, 20.3667, 1, 0, 22, 1, 0)
tracks/61/type = "blend_shape"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("Waterwierd:sk_061")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array(0, 1, 0, 20.0333, 1, 0, 20.3667, 1, 1, 20.7, 1, 0, 22, 1, 0)
tracks/62/type = "blend_shape"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("Waterwierd:sk_062")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array(0, 1, 0, 20.3667, 1, 0, 20.7, 1, 1, 21.0333, 1, 0, 22, 1, 0)
tracks/63/type = "blend_shape"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("Waterwierd:sk_063")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array(0, 1, 0, 20.7, 1, 0, 21.0333, 1, 1, 21.3667, 1, 0, 22, 1, 0)
tracks/64/type = "blend_shape"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("Waterwierd:sk_064")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array(0, 1, 0, 21.0333, 1, 0, 21.3667, 1, 1, 21.7, 1, 0, 22, 1, 0)
tracks/65/type = "blend_shape"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("Waterwierd:sk_065")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array(0, 1, 0, 21.3667, 1, 0, 21.7, 1, 1, 22, 1, 1)

[sub_resource type="AnimationLibrary" id="AnimationLibrary_k7nuw"]
_data = {
&"waterwierd_waterwierdAction": SubResource("Animation_g0q4o")
}

[node name="room1" type="Node3D"]
script = ExtResource("1_3lwtk")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_main8")

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="."]
navigation_mesh = SubResource("NavigationMesh_icseg")

[node name="Map" type="Node3D" parent="NavigationRegion3D"]
unique_name_in_owner = true

[node name="Ground" type="CSGBox3D" parent="NavigationRegion3D/Map"]
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(85.7774, 1, 191.709)

[node name="Ceiling" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1.04789, 0, 9.36679, -43.4679)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(85.7774, 1, 99.3224)

[node name="Wall" type="CSGBox3D" parent="NavigationRegion3D/Map"]
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 10)

[node name="Wall2" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 4.24189, 0, 0)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 10)

[node name="Wall3" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -5.79812, 0, -5.6977)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 10)

[node name="Wall4" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -4.02485, 0, 5.48701)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 10)

[node name="Wall5" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.629939, 0, 9.23984)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 72.2476)

[node name="Wall7" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.0755196, 4.78514, 95.0752)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 85.837)

[node name="Wall9" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.0755196, 4.78514, -95.2098)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 85.837)

[node name="Wall6" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 43.4116, 4.77375, -0.0185528)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 191.577)

[node name="Wall8" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -42.3576, 4.77375, -0.0185528)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(1, 10, 191.577)

[node name="Slope" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(0.913467, -0.406912, 0, 0.406912, 0.913467, 0, 0, 0, 1, -4.3563, 2.05275, 60.4244)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(10, 1, 10)

[node name="Slope2" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.50317, 4.0001, 60.4244)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(7.20565, 1, 10)

[node name="Slope3" type="CSGBox3D" parent="NavigationRegion3D/Map"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15.2162, 4.0001, 60.4244)
use_collision = true
collision_layer = 4
collision_mask = 0
size = Vector3(7.20565, 1, 10)

[node name="NavigationLink3D" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-up"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.51788, 5.55624, 57.8976)
end_position = Vector3(6.24383, -0.168085, 0.0534439)

[node name="NavigationLink3D3" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-up"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.51788, 5.55624, 61.401)
end_position = Vector3(6.24383, -0.168085, 0.0534439)

[node name="NavigationLink3D4" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-up"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6.51788, 5.55624, 65.334)
end_position = Vector3(6.24383, -0.168085, 0.0534439)

[node name="NavigationLink3D2" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-down"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 14.877, 5.41532, 64.9524)
bidirectional = false
end_position = Vector3(0.373163, -4.69097, 1.92145)

[node name="NavigationLink3D5" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-down"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.1178, 5.41532, 64.8656)
bidirectional = false
end_position = Vector3(2.0517, -4.79764, 1.23734)

[node name="NavigationLink3D6" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-down"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.4679, 5.41532, 60.7486)
bidirectional = false
end_position = Vector3(2.16479, -4.24298, 0.00736237)

[node name="NavigationLink3D7" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-down"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.1601, 5.41532, 56.9772)
bidirectional = false
end_position = Vector3(2.55725, -4.84425, -2.45472)

[node name="NavigationLink3D8" type="NavigationLink3D" parent="NavigationRegion3D/Map" groups=["jump-down"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15.3552, 5.41532, 56.6696)
bidirectional = false
end_position = Vector3(1.967, -4.8, -3.146)

[node name="PlayerSpawners" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="Marker3D" type="Marker3D" parent="PlayerSpawners" groups=["player_spawn"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 9.63322, 0.5, 76.1017)

[node name="EnemySpawners" type="Node3D" parent="."]

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3.51703, -21.5044)
light_color = Color(0.501961, 0.2, 1, 1)
light_energy = 4.556
shadow_enabled = true
omni_range = 176.654

[node name="Teleport" parent="." instance=ExtResource("3_6e2tk")]
transform = Transform3D(0.4, 0, 0, 0, 0.4, 0, 0, 0, 0.4, 39.661, 1.74775, 92.5288)
level_name = "res://scenes/rooms/fdm_backrooms.tscn"

[node name="Teleport2" parent="." instance=ExtResource("3_6e2tk")]
transform = Transform3D(0.4, 0, 0, 0, 0.4, 0, 0, 0, 0.4, -39.2033, 1.74775, 92.5288)
level_name = "res://scenes/rooms/mansion_1.tscn"

[node name="Players" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="Enemies" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="HUD" parent="." instance=ExtResource("4_vk5nm")]

[node name="ExternalCameras" type="Node3D" parent="."]

[node name="Node3D" type="Node3D" parent="ExternalCameras"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 37.4032, 1.67316, 42.2671)

[node name="SubViewport2" type="SubViewport" parent="ExternalCameras/Node3D"]
size = Vector2i(323, 323)

[node name="Camera3D2" type="Camera3D" parent="ExternalCameras/Node3D/SubViewport2"]
transform = Transform3D(0.675719, 0, -0.737159, 0, 1, 0, 0.737159, 0, 0.675719, 21.7638, 3.42994, 48.7303)

[node name="Node3D2" type="Node3D" parent="ExternalCameras"]
transform = Transform3D(0.380586, 0, -0.924745, 0, 1, 0, 0.924745, 0, 0.380586, 19.1893, 3.53843, 43.1672)

[node name="SubViewport" type="SubViewport" parent="ExternalCameras/Node3D2"]
size = Vector2i(323, 323)

[node name="Camera3D" type="Camera3D" parent="ExternalCameras/Node3D2/SubViewport"]
transform = Transform3D(-0.999005, 0, 0.0445957, 0.0270158, 0.795622, 0.605191, -0.0354813, 0.605794, -0.79483, 27.464, 5.7748, 40.4373)

[node name="Displays" type="Node3D" parent="."]

[node name="Sprite3D" type="Sprite3D" parent="Displays"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 24.2958, 3.06, 40.5286)
texture = SubResource("ViewportTexture_wodat")

[node name="Sprite3D2" type="Sprite3D" parent="Displays"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 27.6177, 3.06769, 40.5286)
texture = SubResource("ViewportTexture_p8nu4")

[node name="GiantScorpion" parent="." instance=ExtResource("5_p8nu4")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.66408, 0.494847, 47.5417)

[node name="waterwierd" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -31.8673, 0.289931, 15.5065)

[node name="Waterwierd" type="MeshInstance3D" parent="waterwierd"]
transform = Transform3D(0.06, 0, 0, 0, 0.06, 0, 0, 0, 0.06, 0, 0, 0)
mesh = SubResource("ArrayMesh_685ns")
skeleton = NodePath("")
blend_shapes/sk_000 = 1.0
blend_shapes/sk_001 = 0.0
blend_shapes/sk_002 = 0.0
blend_shapes/sk_003 = 0.0
blend_shapes/sk_004 = 0.0
blend_shapes/sk_005 = 0.0
blend_shapes/sk_006 = 0.0
blend_shapes/sk_007 = 0.0
blend_shapes/sk_008 = 0.0
blend_shapes/sk_009 = 0.0
blend_shapes/sk_010 = 0.0
blend_shapes/sk_011 = 0.0
blend_shapes/sk_012 = 0.0
blend_shapes/sk_013 = 0.0
blend_shapes/sk_014 = 0.0
blend_shapes/sk_015 = 0.0
blend_shapes/sk_016 = 0.0
blend_shapes/sk_017 = 0.0
blend_shapes/sk_018 = 0.0
blend_shapes/sk_019 = 0.0
blend_shapes/sk_020 = 0.0
blend_shapes/sk_021 = 0.0
blend_shapes/sk_022 = 0.0
blend_shapes/sk_023 = 0.0
blend_shapes/sk_024 = 0.0
blend_shapes/sk_025 = 0.0
blend_shapes/sk_026 = 0.0
blend_shapes/sk_027 = 0.0
blend_shapes/sk_028 = 0.0
blend_shapes/sk_029 = 0.0
blend_shapes/sk_030 = 0.0
blend_shapes/sk_031 = 0.0
blend_shapes/sk_032 = 0.0
blend_shapes/sk_033 = 0.0
blend_shapes/sk_034 = 0.0
blend_shapes/sk_035 = 0.0
blend_shapes/sk_036 = 0.0
blend_shapes/sk_037 = 0.0
blend_shapes/sk_038 = 0.0
blend_shapes/sk_039 = 0.0
blend_shapes/sk_040 = 0.0
blend_shapes/sk_041 = 0.0
blend_shapes/sk_042 = 0.0
blend_shapes/sk_043 = 0.0
blend_shapes/sk_044 = 0.0
blend_shapes/sk_045 = 0.0
blend_shapes/sk_046 = 0.0
blend_shapes/sk_047 = 0.0
blend_shapes/sk_048 = 0.0
blend_shapes/sk_049 = 0.0
blend_shapes/sk_050 = 0.0
blend_shapes/sk_051 = 0.0
blend_shapes/sk_052 = 0.0
blend_shapes/sk_053 = 0.0
blend_shapes/sk_054 = 0.0
blend_shapes/sk_055 = 0.0
blend_shapes/sk_056 = 0.0
blend_shapes/sk_057 = 0.0
blend_shapes/sk_058 = 0.0
blend_shapes/sk_059 = 0.0
blend_shapes/sk_060 = 0.0
blend_shapes/sk_061 = 0.0
blend_shapes/sk_062 = 0.0
blend_shapes/sk_063 = 0.0
blend_shapes/sk_064 = 0.0
blend_shapes/sk_065 = 0.0

[node name="AnimationPlayer" type="AnimationPlayer" parent="waterwierd"]
libraries = {
&"": SubResource("AnimationLibrary_k7nuw")
}
speed_scale = 3.0
