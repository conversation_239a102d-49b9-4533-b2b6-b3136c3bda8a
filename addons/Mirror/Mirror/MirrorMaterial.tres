[gd_resource type="ShaderMaterial" load_steps=3 format=3 uid="uid://8cn7xcy4msd6"]

[ext_resource type="Shader" path="res://addons/Mirror/Mirror/Mirror.gdshader" id="1"]

[sub_resource type="ViewportTexture" id="12"]
viewport_path = NodePath("SubViewport")

[resource]
resource_local_to_scene = true
render_priority = 0
shader = ExtResource("1")
shader_parameter/tint = Color(0.98, 0.98, 0.98, 1)
shader_parameter/distort_strength = 0.0
shader_parameter/mirror_tex = SubResource("12")
