# Player Integration Example for Shoot Sprites
# This shows how to integrate the new shoot sprites feature with the existing player system

# Add this property to your player.gd script:
var shooting_state: String = ""  # Add this line to enable shoot sprites

# Then add these methods to integrate with the weapon manager:

func _ready():
	# ... existing _ready code ...
	
	# Connect to weapon manager signals for shoot sprite integration
	if weapon_manager:
		# Connect to existing signals
		weapon_manager.weapon_ammo_changed.connect(_on_weapon_ammo_changed)
		weapon_manager.weapon_switched.connect(_on_weapon_switched)
		
		# Add new connections for shooting state
		if weapon_manager.animation_player:
			weapon_manager.animation_player.animation_started.connect(_on_weapon_animation_started)
			weapon_manager.animation_player.animation_finished.connect(_on_weapon_animation_finished)


func _on_weapon_animation_started(anim_name: String):
	"""Called when weapon animation starts - detect shooting animations"""
	if weapon_manager.current_weapon:
		var current_weapon = weapon_manager.current_weapon
		
		# Check if this is a shooting animation
		if (anim_name == current_weapon.shoot_anim_name or 
			anim_name == current_weapon.repeat_shoot_anim_name):
			shooting_state = "shooting"


func _on_weapon_animation_finished(anim_name: String):
	"""Called when weapon animation finishes - stop showing shoot sprites"""
	if weapon_manager.current_weapon:
		var current_weapon = weapon_manager.current_weapon
		
		# Check if this was a shooting animation
		if (anim_name == current_weapon.shoot_anim_name or 
			anim_name == current_weapon.repeat_shoot_anim_name):
			shooting_state = ""


# Alternative approach: Monitor weapon manager state directly
func _physics_process(delta: float) -> void:
	# ... existing _physics_process code ...
	
	# Update shooting state based on weapon manager
	_update_shooting_state()


func _update_shooting_state():
	"""Update shooting state based on weapon manager activity"""
	if not weapon_manager or not weapon_manager.current_weapon:
		shooting_state = ""
		return
	
	var animation_player = weapon_manager.animation_player
	if not animation_player or not animation_player.is_playing():
		shooting_state = ""
		return
	
	var current_anim = animation_player.current_animation
	var current_weapon = weapon_manager.current_weapon
	
	# Check if currently playing a shooting animation
	if (current_anim == current_weapon.shoot_anim_name or 
		current_anim == current_weapon.repeat_shoot_anim_name):
		shooting_state = "shooting"
	else:
		shooting_state = ""


# For auto-hitting weapons, you might want to track the is_auto_hitting state:
func _update_shooting_state_with_auto_hit():
	"""Alternative version that handles auto-hitting weapons"""
	if not weapon_manager or not weapon_manager.current_weapon:
		shooting_state = ""
		return
	
	var current_weapon = weapon_manager.current_weapon
	var animation_player = weapon_manager.animation_player
	
	# For auto-hit weapons, use the is_auto_hitting state
	if current_weapon.auto_hit and weapon_manager.is_auto_hitting:
		shooting_state = "shooting"
		return
	
	# For non-auto weapons, check animation state
	if animation_player and animation_player.is_playing():
		var current_anim = animation_player.current_animation
		if (current_anim == current_weapon.shoot_anim_name or 
			current_anim == current_weapon.repeat_shoot_anim_name):
			shooting_state = "shooting"
			return
	
	shooting_state = ""


# Usage Notes:
# 
# 1. Add `var shooting_state: String = ""` to your player.gd
# 
# 2. The DirectionalSprite3D component will automatically detect this property
#    and show "Shoot sprites" in the inspector
# 
# 3. Configure shoot sprites in the inspector:
#    - front_shoot_sprites: Array of textures for front-facing shooting
#    - side_shoot_sprites: Array of textures for side-facing shooting  
#    - back_shoot_sprites: Array of textures for back-facing shooting
# 
# 4. Choose one of the integration approaches above:
#    - Signal-based: More responsive, requires connecting to animation signals
#    - Polling-based: Simpler, checks state every frame in _physics_process
#    - Auto-hit aware: Handles both regular and auto-hitting weapons
# 
# 5. The shoot sprites will automatically take priority over movement/idle sprites
#    when shooting_state is set to "shooting" or "shot"
# 
# 6. When shooting stops, the component automatically returns to showing
#    movement sprites (if moving) or idle sprites (if stationary)

# Example weapon configuration for shoot sprites:
# 
# In the inspector, you might set up:
# - front_shoot_sprites: [pistol_shoot_front_1.png, pistol_shoot_front_2.png]
# - side_shoot_sprites: [pistol_shoot_side_1.png, pistol_shoot_side_2.png]  
# - back_shoot_sprites: [pistol_shoot_back_1.png, pistol_shoot_back_2.png]
# 
# These will be automatically included in the texture atlas and displayed
# when the player is shooting in the corresponding direction.
