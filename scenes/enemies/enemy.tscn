[gd_scene load_steps=2 format=3 uid="uid://ckn5dttugxo5o"]

[ext_resource type="Script" uid="uid://b7yj3u8isma1g" path="res://scenes/enemies/enemy.gd" id="1_fvloq"]

[node name="Enemy" type="CharacterBody3D" groups=["enemy", "entity"]]
collision_layer = 2
collision_mask = 45
slide_on_ceiling = false
wall_min_slide_angle = 0.0
floor_stop_on_slope = false
floor_block_on_wall = false
floor_snap_length = 0.31
script = ExtResource("1_fvloq")

[node name="Interaction" type="RayCast3D" parent="."]
target_position = Vector3(0, 0, -1.1)
collision_mask = 12

[node name="Graphics" type="Node3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]

[node name="KillZone" type="Area3D" parent="."]
collision_layer = 0

[node name="CollisionShape3D" type="CollisionShape3D" parent="KillZone"]

[node name="Timers" type="Node" parent="."]

[node name="InteractionTimer" type="Timer" parent="Timers"]
wait_time = 0.55
autostart = true

[node name="FindPathTimer" type="Timer" parent="Timers"]
wait_time = 0.1
autostart = true

[node name="WanderingTimer" type="Timer" parent="Timers"]
one_shot = true

[connection signal="link_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_link_reached"]
[connection signal="target_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_target_reached"]
[connection signal="waypoint_reached" from="NavigationAgent3D" to="." method="_on_navigation_agent_3d_waypoint_reached"]
[connection signal="body_entered" from="KillZone" to="." method="_on_kill_zone_body_entered"]
[connection signal="timeout" from="Timers/InteractionTimer" to="." method="_on_interaction_timer_timeout"]
[connection signal="timeout" from="Timers/FindPathTimer" to="." method="_on_find_path_timer_timeout"]
[connection signal="timeout" from="Timers/WanderingTimer" to="." method="_on_wandering_timer_timeout"]
