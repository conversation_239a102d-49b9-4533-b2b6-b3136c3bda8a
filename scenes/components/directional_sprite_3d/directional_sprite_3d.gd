@tool
class_name DirectionalSprite3D
extends Sprite3D

enum DirectionMode {
	THREE_DIRECTIONS, ## front, side, back
	FOUR_DIRECTIONS, ## front, left, right, back
	FIVE_DIRECTIONS, ## front, side, back, front-side, back-side
	EIGHT_DIRECTIONS, ## front, left, right, back, front-left, front-right, back-left, back-right
}

const IDLE_SUFFIX = "_idle_sprite"
const MOVEMENT_SUFFIX = "_movement_sprites"
const SHOOT_SUFFIX = "_shoot_sprites"

# Direction definitions for each mode
const DIRECTION_SETS = {
	DirectionMode.THREE_DIRECTIONS: ["front", "side", "back"],
	DirectionMode.FOUR_DIRECTIONS: ["front", "left", "right", "back"],
	DirectionMode.FIVE_DIRECTIONS: ["front", "side", "back", "front_side", "back_side"],
	DirectionMode.EIGHT_DIRECTIONS: ["front", "left", "right", "back", "front_left", "front_right", "back_left", "back_right"]
}

#region Internal Variables

var has_moving_state := false
var has_shooting_state := false
var idle_sprites := {}
var movement_sprites := {}
var shoot_sprites := {}
var atlas_texture: Texture2D

# Shader material for directional rendering
var directional_material: ShaderMaterial

#endregion


# Alpha cut mode: 0 Disabled,1 Discard,2 Opaque Pre-Pass,3 Alpha Hash
@export_enum("Disabled", "Discard", "Opaque Pre-Pass", "Alpha Hash") var sprite_alpha_cut_mode: int = 0:
	set(value):
		sprite_alpha_cut_mode = value
		if directional_material:
			directional_material.set_shader_parameter("alpha_cut_mode", sprite_alpha_cut_mode)
			directional_material.set_shader_parameter("shaded_enabled", 1 if self.shaded else 0)

# Alpha cut threshold for Discard/Pre-Pass
@export_range(0.0, 1.0, 0.01) var alpha_cut_threshold: float = 0.5:
	set(value):
		alpha_cut_threshold = value
		if directional_material:
			directional_material.set_shader_parameter("alpha_cut_threshold", alpha_cut_threshold)
			directional_material.set_shader_parameter("shaded_enabled", 1 if self.shaded else 0)
			directional_material.set_shader_parameter("shaded_enabled", 1 if self.shaded else 0)


@export var target_node_path: NodePath = NodePath(""):
	set(value):
		target_node_path = value
		_get_target_node()
		generate_atlas()
		notify_property_list_changed()

@export var direction_mode: DirectionMode = DirectionMode.THREE_DIRECTIONS:
	set(value):
		direction_mode = value
		generate_atlas()
		notify_property_list_changed()

@export var debug_mode: bool = false:
	set(value):
		debug_mode = value
		if directional_material:
			directional_material.set_shader_parameter("debug_mode", debug_mode)


func _ready() -> void:
	_get_target_node()
	# Set up the shader material
	if material_override is ShaderMaterial and material_override.shader is Shader:
		directional_material = material_override
		directional_material.set_shader_parameter("alpha_cut_mode", self.alpha_cut)
		directional_material.set_shader_parameter("alpha_cut_threshold", self.alpha_cut_threshold)
		directional_material.render_priority = self.render_priority
	else:
		push_warning("DirectionalSprite3D: Invalid material override. Expected ShaderMaterial with Shader.")


func _process(_delta: float) -> void:
	# Update per-frame shader parameters
	if directional_material and directional_material.shader:
		var target_node = _get_target_node()
		if target_node:
			directional_material.set_shader_parameter("target_position", target_node.global_position)

			# Update sprite state based on target's current state
			var sprite_state = _get_current_sprite_state(target_node)
			directional_material.set_shader_parameter("sprite_state", sprite_state)

		# Synchronise shader parameters each frame
		directional_material.set_shader_parameter("alpha_cut_mode", self.alpha_cut)
		directional_material.set_shader_parameter("alpha_cut_threshold", self.alpha_cut_threshold)
		directional_material.render_priority = self.render_priority


func _get(property: StringName):
	var prop_name = str(property)

	if prop_name.ends_with(IDLE_SUFFIX):
		var direction = prop_name.replace(IDLE_SUFFIX, "")
		return idle_sprites.get(direction)

	if prop_name.ends_with(MOVEMENT_SUFFIX):
		var direction = prop_name.replace(MOVEMENT_SUFFIX, "")
		if not movement_sprites.has(direction):
			movement_sprites[direction] = []
		return movement_sprites[direction]

	if prop_name.ends_with(SHOOT_SUFFIX):
		var direction = prop_name.replace(SHOOT_SUFFIX, "")
		if not shoot_sprites.has(direction):
			shoot_sprites[direction] = []
		return shoot_sprites[direction]

	return null


func _set(property: StringName, value) -> bool:
	var prop_name = str(property)
	
	if prop_name == "billboard":
		if directional_material:
			directional_material.set_shader_parameter("billboard_mode", value)
	
	if prop_name == "render_priority":
		if directional_material:
			directional_material.render_priority = value


	if prop_name.ends_with(IDLE_SUFFIX):
		var direction = prop_name.replace(IDLE_SUFFIX, "")
		if direction in _get_current_directions():
			idle_sprites[direction] = value
			generate_atlas()
			return true

	if prop_name.ends_with(MOVEMENT_SUFFIX):
		var direction = prop_name.replace(MOVEMENT_SUFFIX, "")
		if direction in _get_current_directions():
			movement_sprites[direction] = value
			generate_atlas()
			return true

	if prop_name.ends_with(SHOOT_SUFFIX):
		var direction = prop_name.replace(SHOOT_SUFFIX, "")
		if direction in _get_current_directions():
			shoot_sprites[direction] = value
			generate_atlas()
			return true

	return false


func _get_property_list():
	var properties: Array[Dictionary] = []
	var directions = _get_current_directions()

	_add_sprite_group_properties(properties, "Idle sprites", directions, IDLE_SUFFIX, TYPE_OBJECT, "Texture2D")

	if has_moving_state:
		_add_sprite_group_properties(properties, "Movement sprites", directions, MOVEMENT_SUFFIX, TYPE_ARRAY, "%d/%d:Texture2D" % [TYPE_OBJECT, PROPERTY_HINT_RESOURCE_TYPE])

	if has_shooting_state:
		_add_sprite_group_properties(properties, "Shoot sprites", directions, SHOOT_SUFFIX, TYPE_ARRAY, "%d/%d:Texture2D" % [TYPE_OBJECT, PROPERTY_HINT_RESOURCE_TYPE])

	return properties


func _get_current_directions() -> Array:
	return DIRECTION_SETS.get(direction_mode, [])


func _get_current_sprite_state(target_node: Node) -> int:
	"""Determine which sprite state should be displayed based on target's current state.

	Priority order:
	1. Shooting state (if target is shooting)
	2. Movement state (if target is moving)
	3. Idle state (default fallback)

	Returns:
		int: 0 = idle, 1 = movement, 2 = shooting
	"""
	if not target_node:
		return 0 # Default to idle

	# Check for shooting state (highest priority)
	if has_shooting_state and target_node.has_method("get") and target_node.get("shooting_state"):
		var shooting_state = target_node.get("shooting_state")
		if shooting_state == "shooting" or shooting_state == "shot":
			return 2 # Shooting state

	# Check for movement state (medium priority)
	if has_moving_state and target_node.has_method("get") and target_node.get("moving_state"):
		var moving_state = target_node.get("moving_state")
		if moving_state == "run" or moving_state == "moving":
			return 1 # Movement state

	# Default to idle state (lowest priority)
	return 0 # Idle state


func _add_sprite_group_properties(properties: Array[Dictionary], group_name: String, directions: Array, suffix: String, property_type: int, hint_string: String) -> void:
	properties.append({
		"name": group_name,
		"type": TYPE_NIL,
		"usage": PROPERTY_USAGE_GROUP,
	})

	var hint_type = PROPERTY_HINT_RESOURCE_TYPE if property_type == TYPE_OBJECT else PROPERTY_HINT_ARRAY_TYPE

	for direction in directions:
		properties.append({
			"name": direction + suffix,
			"type": property_type,
			"hint": hint_type,
			"hint_string": hint_string,
			"usage": PROPERTY_USAGE_DEFAULT
		})


func _get_target_node() -> Node3D:
	var target_node: Node = null
	if not target_node_path.is_empty() and has_node(target_node_path):
		target_node = get_node(target_node_path)
	else:
		target_node = get_parent()

	if target_node and target_node.get_script():
		var script_properties = target_node.get_script().get_script_property_list()
		has_moving_state = script_properties.any(func(prop): return prop.name == "moving_state")
		has_shooting_state = script_properties.any(func(prop): return prop.name == "shooting_state")
	else:
		has_moving_state = false
		has_shooting_state = false
	return target_node


func generate_atlas():
	if not _has_any_sprites():
		atlas_texture = null
		return null
	
	var directions = _get_current_directions()
	if directions.is_empty():
		push_warning("DirectionalSprite3D: No directions available for atlas generation")
		return null
	
	# Validate sprite dimensions before proceeding
	if not _validate_sprite_dimensions(directions):
		return null
		
	# Collect sprites and determine dimensions
	var all_sprites: Array[Array] = []
	var max_sprite_size = _get_sprite_max_dimensions(directions)
	var max_frames = 1
	
	# Collect all sprites for each direction
	for direction in directions:
		var direction_sprites = _collect_direction_sprites(direction)
		all_sprites.append([direction, direction_sprites])
		max_frames = max(max_frames, direction_sprites.size())
	
	# Create and populate atlas
	var atlas_dimensions = Vector2i(max_sprite_size.x * max_frames, max_sprite_size.y * directions.size())
	atlas_texture = _create_atlas_texture(all_sprites, atlas_dimensions, max_sprite_size)
	
	if atlas_texture:
		# Create properly sized current sprite texture
		#_update_current_sprite_texture(sprite_size)
		# Update shader uniforms when atlas changes
		#call_deferred("_update_shader_uniforms")
		var image = Image.create(max_sprite_size.x, max_sprite_size.y, false, Image.FORMAT_RGBA8)
		image.fill(Color.TRANSPARENT)
		texture = ImageTexture.create_from_image(image)

		# Ensure we have the shader material set up
		if not directional_material:
			var shader = load("res://scenes/components/directional_sprite_3d/directional_sprite_3d.gdshader")
			directional_material = ShaderMaterial.new()
			directional_material.shader = shader
			material_override = directional_material
		
		if directional_material and directional_material.shader:
			directional_material.set_shader_parameter("atlas_texture", atlas_texture)
			directional_material.set_shader_parameter("billboard_mode", billboard)
			var atlas_size = Vector2(atlas_texture.get_width(), atlas_texture.get_height())
			directional_material.set_shader_parameter("atlas_dimensions", atlas_size)
			directional_material.set_shader_parameter("max_sprite_size", Vector2(max_sprite_size))
			directional_material.set_shader_parameter("direction_mode", direction_mode)
			directional_material.set_shader_parameter("alpha_cut_mode", self.alpha_cut)
			directional_material.set_shader_parameter("alpha_cut_threshold", self.alpha_cut_threshold)
			directional_material.render_priority = self.render_priority
			directional_material.set_shader_parameter("debug_mode", debug_mode)

			# Calculate frame counts for shader
			var frame_counts = _calculate_frame_counts()
			directional_material.set_shader_parameter("movement_frame_count", frame_counts.movement)
			directional_material.set_shader_parameter("shoot_frame_count", frame_counts.shoot)

			# Target position will be updated in _process

		notify_property_list_changed()


func _has_any_sprites() -> bool:
	# Check idle sprites
	for direction in idle_sprites:
		var sprite = idle_sprites[direction]
		if sprite != null and sprite is Texture2D:
			return true

	# Check movement sprites
	for direction in movement_sprites:
		var sprite_array = movement_sprites[direction]
		if sprite_array is Array and sprite_array.size() > 0:
			for sprite in sprite_array:
				if sprite != null and sprite is Texture2D:
					return true

	# Check shoot sprites
	for direction in shoot_sprites:
		var sprite_array = shoot_sprites[direction]
		if sprite_array is Array and sprite_array.size() > 0:
			for sprite in sprite_array:
				if sprite != null and sprite is Texture2D:
					return true

	return false

## Validates that all sprites have reasonable dimensions for atlas generation
func _validate_sprite_dimensions(directions: Array) -> bool:
	var max_dimensions = _get_sprite_max_dimensions(directions)
	if max_dimensions == Vector2i.ZERO:
		push_warning("DirectionalSprite3D: No valid sprites found for atlas generation")
		return false
	
	# Check for extremely large textures that might cause memory issues
	if max_dimensions.x > 2048 or max_dimensions.y > 2048:
		push_warning("DirectionalSprite3D: Large sprite dimensions detected (%dx%d). Consider using smaller textures for better performance." % [max_dimensions.x, max_dimensions.y])
	
	return true


func _get_sprite_max_dimensions(directions: Array) -> Vector2i:
	var max_width = 0
	var max_height = 0
	
	# Scan all sprites to find maximum dimensions
	for direction in directions:
		# Check idle sprite
		var idle_sprite = idle_sprites.get(direction)
		if idle_sprite is Texture2D:
			var dimensions = _get_texture_dimensions(idle_sprite)
			max_width = max(max_width, dimensions.x)
			max_height = max(max_height, dimensions.y)
		
		# Check movement sprites
		var movement_sprites_array = movement_sprites.get(direction, [])
		for sprite in movement_sprites_array:
			if sprite is Texture2D:
				var dimensions = _get_texture_dimensions(sprite)
				max_width = max(max_width, dimensions.x)
				max_height = max(max_height, dimensions.y)
	
	return Vector2i(max_width, max_height)


func _get_texture_dimensions(tex: Texture2D) -> Vector2i:
	var image = tex.get_image()
	if image == null:
		return Vector2i.ZERO
	
	if image.is_compressed():
		image.decompress()
	
	return Vector2i(image.get_width(), image.get_height())


func _collect_direction_sprites(direction: String) -> Array[Texture2D]:
	var direction_sprites: Array[Texture2D] = []

	# Add idle sprite or placeholder
	var idle_sprite = idle_sprites.get(direction)
	if idle_sprite is Texture2D:
		direction_sprites.append(idle_sprite)
	else:
		direction_sprites.append(null)

	# Add movement sprites
	var movement_sprite_array = movement_sprites.get(direction, [])
	if movement_sprite_array is Array:
		for sprite in movement_sprite_array:
			if sprite is Texture2D:
				direction_sprites.append(sprite)

	# Add shoot sprites
	var shoot_sprite_array = shoot_sprites.get(direction, [])
	if shoot_sprite_array is Array:
		for sprite in shoot_sprite_array:
			if sprite is Texture2D:
				direction_sprites.append(sprite)

	return direction_sprites


func _calculate_frame_counts() -> Dictionary:
	"""Calculate the maximum number of frames for movement and shoot sprites across all directions.

	Returns:
		Dictionary: {"movement": int, "shoot": int}
	"""
	var max_movement_frames = 0
	var max_shoot_frames = 0

	var directions = _get_current_directions()
	for direction in directions:
		# Count movement frames
		var movement_sprite_array = movement_sprites.get(direction, [])
		if movement_sprite_array is Array:
			max_movement_frames = max(max_movement_frames, movement_sprite_array.size())

		# Count shoot frames
		var shoot_sprite_array = shoot_sprites.get(direction, [])
		if shoot_sprite_array is Array:
			max_shoot_frames = max(max_shoot_frames, shoot_sprite_array.size())

	return {"movement": max_movement_frames, "shoot": max_shoot_frames}


func _create_atlas_texture(all_sprites: Array[Array], atlas_dimensions: Vector2i, max_sprite_size: Vector2i) -> ImageTexture:
	var atlas_image = Image.create_empty(atlas_dimensions.x, atlas_dimensions.y, false, Image.FORMAT_RGBA8)
	atlas_image.fill(Color.TRANSPARENT)
	
	# Blit sprites into atlas
	var row = 0
	for sprite_data in all_sprites:
		var sprite_array = sprite_data[1]
		
		for col in range(sprite_array.size()):
			var sprite = sprite_array[col]
			if sprite is Texture2D:
				_blit_sprite_to_atlas(sprite, atlas_image, col, row, max_sprite_size)
		
		row += 1
	
	# Create texture
	var new_atlas_texture = ImageTexture.new()
	new_atlas_texture.set_image(atlas_image)

	if new_atlas_texture.get_width() == 0 or new_atlas_texture.get_height() == 0:
		push_error("DirectionalSprite3D: Failed to create atlas texture")
		return null

	return new_atlas_texture


func _blit_sprite_to_atlas(sprite: Texture2D, atlas_image: Image, col: int, row: int, max_sprite_size: Vector2i):
	var sprite_image = sprite.get_image()
	if sprite_image == null:
		return
	
	# Handle compressed textures
	if sprite_image.is_compressed():
		sprite_image.decompress()
	
	# Convert to atlas format
	if sprite_image.get_format() != Image.FORMAT_RGBA8:
		sprite_image.convert(Image.FORMAT_RGBA8)
	
	# Get actual sprite dimensions
	var actual_width = sprite_image.get_width()
	var actual_height = sprite_image.get_height()
	
	# Calculate atlas cell position
	var cell_pos = Vector2i(col * max_sprite_size.x, row * max_sprite_size.y)
	
	# Center the sprite within the atlas cell if it's smaller
	var offset_x = (max_sprite_size.x - actual_width) / 2.0
	var offset_y = (max_sprite_size.y - actual_height) / 2.0
	var dest_pos = Vector2i(cell_pos.x + offset_x, cell_pos.y + offset_y)
	
	# Ensure we don't exceed atlas cell boundaries
	var blit_width = min(actual_width, max_sprite_size.x)
	var blit_height = min(actual_height, max_sprite_size.y)
	var src_rect = Rect2i(0, 0, blit_width, blit_height)
	
	# Adjust destination if sprite is larger than cell (crop from center)
	if actual_width > max_sprite_size.x or actual_height > max_sprite_size.y:
		var crop_offset_x = (actual_width - max_sprite_size.x)
		var crop_offset_y = (actual_height - max_sprite_size.y)
		src_rect = Rect2i(crop_offset_x, crop_offset_y, max_sprite_size.x, max_sprite_size.y)
		dest_pos = cell_pos
	
	atlas_image.blit_rect(sprite_image, src_rect, dest_pos)
