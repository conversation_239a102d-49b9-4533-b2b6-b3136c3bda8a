[gd_scene load_steps=4 format=3 uid="uid://c8ewg3wlp1sw3"]

[ext_resource type="Script" uid="uid://0lax4sx4ont5" path="res://scenes/objects/break_glass.gd" id="1_407mq"]
[ext_resource type="Texture2D" uid="uid://bqx0tlmurelha" path="res://images/objects/glasses/1176A0.png" id="1_u7ku3"]

[sub_resource type="BoxShape3D" id="BoxShape3D_8tps6"]
size = Vector3(0.0703125, 0.101807, 0.0634766)

[node name="Glass4" type="StaticBody3D"]
collision_layer = 4
collision_mask = 7
script = ExtResource("1_407mq")

[node name="Sprite3D" type="Sprite3D" parent="."]
billboard = 2
shaded = true
texture = ExtResource("1_u7ku3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_8tps6")
